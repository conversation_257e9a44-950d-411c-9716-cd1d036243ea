<template>
  <clientOnly>
    <div
      class="select-question"
      :style="{ marginTop: isFouce ? '-50px' : '0' }"
    >
      <div class="title">
        <div>
          <text v-if="info.type != 9">[{{ info.type_name }}题型]</text>
          <text v-else>[{{ info.type_name }}]</text>
          <text v-if="stem.multiple && info.type != 9">（多选）</text>
        </div>
        <div v-if="info.thematic_stem">病例：{{ info.thematic_stem }}</div>
        <div class="question_content">
          <div class="question_number">{{ questionNumber }}、</div>
          <div v-html="stem.content" v-if="stem.content" />
        </div>
        <!-- <div v-if="stem.content && stem.content.includes('audio')">
          <div
            class="audio"
            v-for="(src, a) in getAudioAll(stem.content)"
            :key="a"
          >
            <ModuleAnswerMyAudio
              style="text-align: left"
              :music="src"
              :name="'[' + info.type_name + '题型]'"
              controls
            />
          </div>
        </div> -->
        <!-- 这里是图片和音频的单独解析区 -->

        <clientOnly>
          <ModuleAnswerRenderImgAndVideo
            :stem="stem"
            v-if="
              stem.resource_info &&
              (stem.resource_info.img || stem.resource_info.video)
            "
            ref="audioHandle"
          />
        </clientOnly>
      </div>
      <!-- 渲染简答题 -->
      <div v-if="isSubjective(info.type) && !answer">
        <textarea
          class="textarea"
          maxlength="9999"
          v-model="info.sub_answer"
          placeholder="请输入"
          :cursor="60"
          :adjust-position="true"
          :show-confirm-bar="false"
          @input="onInputChange"
          @focus="isFouce = true"
          @blur="isFouce = false"
        />
      </div>
      <!-- 渲染填空题 做题时候的渲染-->
      <div v-else-if="isFillBlanks(info.type) && !answer">
        <div
          class="tk-options flex"
          v-for="(item, index) in stem.answer"
          :key="index"
        >
          <text>({{ index + 1 }})、</text>
          <input
            class="uni-input"
            :placeholder="`请输入第${index + 1}个填空内容`"
            placeholder-class="placeholder-input"
            :value="stem.selected[index]"
            @input="tkinput($event, index)"
          />
        </div>
      </div>
      <!-- 渲染填空题 看答案时候的渲染 -->
      <div v-else-if="isFillBlanks(info.type) && answer">
        <!-- 渲染空数据 -->
      </div>
      <!-- 渲染非简答题就是选择题 v-if="!isSubjective(info.type)" -->
      <div class="selects" v-else>
        <div
          class="select"
          v-for="(item, index) in stem?.option"
          :key="index"
          :class="{
            selected: stem?.selected?.includes(index) && !answer,
            success: answer && stem?.answer?.includes(index),
            error:
              answer &&
              stem?.selected?.includes(index) &&
              !stem?.answer?.includes(index),
          }"
          @click="select(item, index)"
        >
          <div class="english">{{ transition[index] }}</div>
          <div class="line" />
          <div class="desc" v-html="item" />
        </div>
      </div>
    </div>
  </clientOnly>
</template>
<script>
export default {
  name: "select-question", // 选择题
  props: {
    info: {
      type: Object,
    },
    stem: {
      type: Object,
    },
    answer: {
      // 是否是解析阶段
      type: Boolean,
      default: false,
    },
    isShowAnalysis: {
      // 是否显示解析了
      type: Boolean,
      default: false,
    },
    questionNumber: {
      type: Number,
      default: 1,
    },
    current: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      key: 0,
      transition: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"],
      editorCtx: null,
      id: "edit" + (Math.random() * 100).toFixed(0),
      isFouce: false,
      currentNmuber: 0,
    }
  },
  watch: {
    current: {
      handler(nv, ov) {
        console.log("我变了")
        this.$refs.audioHandle?.stopPlay && this.$refs.audioHandle?.stopPlay()
      },
    },
  },
  methods: {
    isSubjective(type) {
      return type == "8" || type == "10" || type == "14" // 14案例题
    },
    isFillBlanks(type) {
      return type == "9"
    },
    select(item, index) {
      console.log("item", item, index)
      if (this.answer || this.isShowAnalysis) {
        // 如果有答案了 就不让选了
        return
      }
      if (this.stem.multiple) {
        // 多选题逻辑
        let isSelece = this.stem.selected.includes(index)
        if (!isSelece) {
          // 多选题的选中
          this.stem.selected.push(index)
        } else {
          // 多选题的取消选中
          let idx = this.stem.selected.indexOf(index)
          this.stem.selected.splice(idx, 1)
        }
      } else {
        // 单选题的选中
        this.stem.selected.pop()
        this.stem.selected.push(index)
      }
      this.$emit("selecte", {
        ...this.info,
        stem_list: [
          this.stem, // 当前这个小题的所有信息
          ...this.info.stem_list.filter((item) => {
            return item.id != this.stem.id
          }),
        ],
        user_option: this.stem.selected.join(","),
      })
    },
    tkinput(e, i) {
      // let value = e.detail.value
      let value = e.data
      this.stem.selected[i] = value
      this.$emit("selecte", {
        ...this.info,
        stem_list: [
          this.stem,
          ...this.info.stem_list.filter((item) => {
            return item.id != this.stem.id
          }),
        ],
        user_option: this.stem.selected.join(","),
      })
      console.log(this.info)
    },
    onInputChange(e) {
      if (isSubjective(this.info.type) || isFillBlanks(this.info.type)) {
        // 8就是简答题
        this.stem.selected[0] = this.info.sub_answer
        this.$emit("input", {
          ...this.info,
          stem_list: [
            this.stem,
            ...this.info.stem_list.filter((item) => {
              return item.id != this.stem.id
            }),
          ],
          user_option: this.stem.selected.join(","),
        })
      }
    },
    focus() {
      uni.pageScrollTo({
        scrollTop: 0, // 滚动到页面的目标位置（单位px）
        duration: 300, // 滚动动画的时长，默认300ms，单位 ms
      })
    },
    getAudio(html) {
      try {
        const regex = /<audio\s*src="([^"]*)"[^>]*>/
        const match = html.match(regex)
        if (match && match[1]) {
          return match[1]
        }
      } catch (error) {
        return ""
      }
      return ""
    },
    getAudioAll(html) {
      try {
        const regex = /<audio\s*src="([^"]*)"[^>]*>/g
        const matches = []
        let match
        while ((match = regex.exec(html)) !== null) {
          matches.push(match[1])
        }
        return matches
      } catch (error) {
        console.log(error)
        return []
      }
    },
  },
  mounted() {},
}
</script>
<style lang="scss" scoped>
.editor-box {
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 10px;
  .editor {
    width: 100%;
    height: 300px;
    background-color: #fff;
  }
}
.audio {
  width: 100%;
  height: auto;
  margin-top: 10px;
}
.select-question {
  .title {
    font-size: 16px;
    color: #000000;
    line-height: 24px;
    margin-bottom: 26px;
    view:first-child {
      color: #387dfc;
      margin-right: 2px;
    }
    .question_content {
      margin-top: 6px;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      .question_number {
        color: #000 !important;
      }
    }
  }
  .selects {
    .select {
      width: 100%;
      min-height: 30px;
      border-radius: 25px;
      border: 1px solid #ececec;
      display: flex;
      align-items: center;
      padding: 15px 20px;
      margin-bottom: 16px;
      .english {
        width: 11px;
        font-weight: 600;
        margin-right: 14px;
        color: #333333;
      }
      .line {
        width: 1px;
        height: 16px;
        background-color: #cacaca;
        margin-right: 14px;
      }
      .desc {
        flex: 1;
        font-size: 16px;
      }
    }
    .select:last-child {
      margin-bottom: 0;
    }
    .selected {
      border: 1px solid #567dfa;
      background: rgba(124, 191, 247, 0.18);
    }
    .error {
      position: relative;
      border: 1px solid rgba(247, 119, 105, 0.6);
      background: #fff3f2;
      &::before {
        position: absolute;
        content: "";
        right: 14px;
        top: 0;
        bottom: 0;
        margin: auto 0;
        width: 24px;
        height: 24px;
        background: url("https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950952878505e2f169509528785094708_error.png")
          no-repeat;
        background-size: cover;
      }
    }
    .success {
      border: 1px solid rgba(132, 124, 247, 0.5);
      background: rgba(132, 124, 247, 0.18);
    }
  }
  .textarea {
    border: 1px solid #ccc;
    width: 100%;
    border-radius: 16px;
    margin-bottom: 26px;
    padding: 12px;
    box-sizing: border-box;
    font-size: 13px;
    height: 300px;
  }
  .save {
    display: flex;
    justify-content: flex-end;
    .save_button {
      width: 50px;
      height: 28px;
      background: #2574fe;
      border-radius: 4px;
      text-align: center;
      line-height: 28px;
      color: #fff;
      font-size: 14px;
    }
  }
}
.img-and-video {
  width: 100%;
  position: relative;
  .bg-img {
    width: 100%;
    height: auto;
  }
  .position {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    image {
      width: 25px;
      height: 25px;
    }
  }
  .video {
    width: 100%;
    .myVideo {
      width: 100%;
    }
  }
}
.ripple-effect {
  position: absolute;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation-name: ripple-animate;
  animation-duration: 1s;
  animation-timing-function: ease-out;
  animation-iteration-count: infinite; /* 设置为无限循环播放 */
}
@keyframes ripple-animate {
  to {
    transform: scale(2.5);
    opacity: 0;
  }
}
.tk-options {
  margin-bottom: 16px;
  width: 100%;
  text {
    font-size: 16px;
    color: #333;
    margin-right: 9px;
  }
  input {
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 0px 20px;
    flex: 1;
    height: 33px;
    line-height: 33px;
    font-size: 14px;
  }
  .placeholder-input {
    color: #eee;
  }
}
</style>
