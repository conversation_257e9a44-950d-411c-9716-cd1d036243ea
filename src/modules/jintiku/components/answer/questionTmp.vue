<template>
  <div class="question_a1">
    <div class="title_box">
      <div
        class="thematic_stem question_title"
        v-if="isOrdinaryCases(data.type)"
      >
        <div>{{ data.sort }}、病例：</div>
        <div v-html="data.thematic_stem" class="content" />
      </div>
      <div class="question_title" v-if="getQuestionTitleShow(data.type)">
        <div>{{ data.sort }}、</div>
        <div style="display: flex">
          <div
            v-if="data.stem_list && data.stem_list.length"
            v-html="data.stem_list[0].content"
            style="word-break: break-all"
          ></div>
          <div v-else>本题暂无题目</div>
          <!-- 里面有音频就渲染音频 -->
          <!-- <div
            v-if="data.stem_list[0].content.includes('audio')"
            style="width: 275px"
          >
            <div
              class="audio"
              v-for="(src, a) in getAudioAll(data.stem_list[0].content)"
              :key="a"
            >
              <my-audio
                :music="src"
                :name="'(' + (i + 1) + ')'"
                controls
              ></my-audio>
            </div>
          </div> -->
        </div>
      </div>
      <!-- 这里是图片和音频的单独解析区 -->
      <clientOnly>
        <ModuleAnswerRenderImgAndVideo
          :stem="data"
          v-if="
            data.resource_info &&
            (data.resource_info.img.path || data.resource_info.video.path)
          "
        />
      </clientOnly>
      <!-- <render-img-and-video
        :stem="data"
        v-if="
          data.resource_info &&
          (data.resource_info.img.path || data.resource_info.video.path)
        "
      /> -->
      <div class="question_title select_title" v-if="data.type == 5">
        {{ data.sort }}、B1第{{ question_sort + 1 }}组题
      </div>
      <!-- <div class="question_title select_title" v-if="data.type == 5">
        <div
          class="select"
          v-for="(item, index) in JSON.parse(data.stem_list[0].option)"
          :key="index"
        >
          <div>{{ selectList[index] }}.</div>
          <div v-html="item" style="word-break: break-all"></div>
        </div>
      </div> -->
    </div>
    <div v-for="(info, i) in data.stem_list" :key="i">
      <div
        v-if="isOrdinaryCases(data.type) || data.type == 5"
        class="select_list_title"
      >
        <div>({{ i + 1 }})、</div>
        <div
          v-html="info.content ? info.content : '暂无'"
          style="word-break: break-all"
        ></div>
        <div v-if="info.content.includes('audio')" style="width: 275px">
          <div
            class="audio"
            v-for="(src, a) in getAudioAll(info.content)"
            :key="a"
          >
            <!-- <my-audio
              :music="src"
              :name="'(' + (i + 1) + ')'"
              controls
            ></my-audio> -->
          </div>
        </div>
      </div>
      <div class="select_box" v-if="!isSubjective(data.type)">
        <div class="select" v-for="(item, index) in info.option" :key="index">
          <div>{{ selectList[index] }}.</div>
          <div v-html="item"></div>
        </div>
      </div>
      <div class="explain_box" style="margin-top: 12px" v-else>
        <div class="explain">作答：</div>
        <div
          class="content"
          v-html="
            info.sub_answer.length
              ? info.sub_answer.join('') || '未作答'
              : '未作答'
          "
        ></div>
      </div>
      <div class="is_right">
        <div
          class="is_right_button correct"
          :class="{
            correct: info.answer_status == 1,
            error: info.answer_status == 2,
            half: info.answer_status == 3,
          }"
          v-if="info.answer_status != 0"
        >
          {{ getStateText(info.answer_status) }}
        </div>
        <div
          class="score"
          :class="{
            blue_text: info.answer_status == 1 || isSubjective(data.type),
            red_text: info.answer_status == 2,
            yellow_text: info.answer_status == 3,
          }"
          style="display: flex"
        >
          得分：
          <!-- 不是简答题 -->
          <div v-if="!isSubjective(data.type)">{{ info.get_score }}分</div>
          <!-- 阅卷结束 && 是简答题 -->
          <div v-else-if="isCorrect == 1 && isSubjective(data.type)">
            {{ info.get_score }}分
          </div>
          <!-- 阅卷中 && 是简答题 -->
          <div v-else-if="isCorrect == 2 && isSubjective(data.type)">
            待阅卷
          </div>
        </div>
      </div>
      <div class="answer" v-if="!isSubjective(data.type)">
        <div
          class="blue_text"
          v-if="info.answer_status != 1 && isSelectedType(data.type)"
          style="margin-right: 11px; display: flex"
        >
          <div style="flex-shrink: 0">正确答案：</div>
          <div>{{ info.answerName.sort().join("、") }}</div>
        </div>
        <div style="color: #000000; display: flex">
          <div style="flex-shrink: 0">您的答案：</div>
          <!-- 选择题 -->
          <div v-if="isSelectedType(data.type)">
            {{ info.sub_answer_name.sort().join("、") || "未作答" }}
          </div>
          <!-- 填空题 -->
          <div v-if="isFillBlanks(data.type)">
            <!-- <text
              v-for="(text, j) in info.sub_answer"
              :key="j"
              style="margin-right: 4px"
            >
              {{ text }}
            </text> -->{{ info.sub_answer.join("、") }}
          </div>
        </div>
      </div>
    </div>
    <div class="explain_box">
      <div class="explain">解析：</div>
      <div v-html="data.parse" class="content" v-if="data.parse" />
      <div v-else class="content">暂无解析</div>
    </div>
    <!-- <div v-if="data.parse && data.parse.includes('audio')" style="width: 275px">
      <div class="audio" v-for="(src, a) in getAudioAll(data.parse)" :key="a">
        <my-audio
          style="text-align: left"
          :music="src"
          name="解析"
          controls
        ></my-audio>
      </div>
    </div> -->
    <div class="explain_box">
      <div class="explain">知识点：</div>
      <div
        v-html="data.knowledge_ids_name"
        class="content"
        v-if="data.knowledge_ids_name"
      />
      <div v-else class="content">暂无</div>
    </div>
    <div class="explain_box">
      <div class="explain">难易度：</div>
      <div class="star">
        <div v-for="item in 5" :key="item" style="margin-right: 5px">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16953700953269c48169537009532642625_%E7%BC%96%E7%BB%84%E5%A4%87%E4%BB%BD%205%402x.png"
            alt=""
            v-if="item < data.level"
          />
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16953700763015ff616953700763011336_Fill%202%402x.png"
            alt=""
            v-else
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "question-a-one", // 答题详情
  props: {
    data: {},
    isCorrect: {
      type: String,
    },
    question_sort: {},
  },
  data() {
    return {
      selectList: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"],
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      console.log("this", this.data.stem_list)
      if (!this.data.stem_list) {
        return
      }
      this.data.stem_list.map((res) => {
        // if (isSubjective(this.data.type)) {
        //   return false
        // }
        try {
          res.answerName = JSON.parse(res.answer).map((item) => {
            return this.selectList[item] ? this.selectList[item] : "暂无"
          })
        } catch (error) {
          res.answerName = []
        }
        try {
          res.sub_answer = JSON.parse(res.sub_answer)
        } catch (error) {
          res.sub_answer = []
        }
        try {
          res.sub_answer_name = res.sub_answer
            ? res.sub_answer.map((item) => {
                return this.selectList[item] ? this.selectList[item] : ""
              })
            : []
        } catch (error) {}
        try {
          res.option = JSON.parse(res.option)
        } catch (error) {
          res.option = []
        }
      })
    },
    getStateText(type) {
      switch (type) {
        case "1":
          return "正确"
        case "2":
          return "错误"
        case "3":
          return "半对"
        default:
          return "--"
      }
    },
    // getAudioAll: getAudioAll,
    getQuestionTitleShow(type) {
      return (
        this.isSingle(type) ||
        this.isMultiple(type) ||
        this.isFillBlanks(type) ||
        this.isRightWrong(type) ||
        this.isSubjective(type)
      )
    },
    isSubjective(type) {
      return type == "8" || type == "10" || type == "14" // 14案例题
    },
    getAudioAll(html) {
      try {
        const regex = /<audio\s*src="([^"]*)"[^>]*>/g
        const matches = []
        let match
        while ((match = regex.exec(html)) !== null) {
          matches.push(match[1])
        }
        return matches
      } catch (error) {
        console.log(error)
        return []
      }
    },
    isFillBlanks(type) {
      // TODO
      return type == "9"
    },
    isSelectedType(type) {
      return !this.isFillBlanks(type) && !this.isSubjective(type)
    },
    isSingle(type) {
      return type == "1" || type == "2" || type == "12"
    },
    isMultiple(type) {
      return type == "7" || type == "13"
    },
    isRightWrong(type) {
      return type == "11"
    },
    isOrdinaryCases(type) {
      return type == "3" || type == "4" || type == "6"
    },
  },
}
</script>
<style lang="scss" scoped>
.question_a1 {
  padding: 10px 12px;
  border: 1px solid #eeeeee;
  border-radius: 6px;
  .title_box {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    font-size: 13px;
    color: #161f30;
    .question_title {
      display: flex;
      // flex-wrap: wrap;
      position: relative;
    }
    .thematic_stem {
      flex-wrap: wrap;
      div:last-child {
        flex: 1;
      }
    }
    .select_title {
      flex: 1;
      width: 100%;
      flex-wrap: wrap;
      .select {
        display: flex;
        width: 45%;
        margin: 10px 10px 0 0;
      }
    }
  }
  .select_list_title {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    font-size: 13px;
    color: #161f30;
    margin-top: 10px;
  }
  .select_box {
    font-size: 13px;
    color: #161f30;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .select {
      // flex: 0.5;
      margin: 10px 10px 0 0;

      width: 45%;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
    }
  }
  .is_right {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 10px 0 13px 0;
    font-size: 14px;
    .is_right_button {
      width: 50px;
      height: 28px;
      text-align: center;
      line-height: 28px;
      border-radius: 4px;
      color: #ffffff;
      margin-right: 10px;
    }
    .correct {
      background: #2e68ff;
    }
    .error {
      background: #f04f54;
    }
    .half {
      background: #f99300;
    }
    .score {
      margin-right: 10px;
    }
    .error_score {
      color: #f04f54;
    }
    .blue_text {
      color: #2e68ff;
    }
    .red_text {
      color: #f04f54;
    }
    .yellow_text {
      color: #f99300;
    }
  }
  .answer {
    margin: 13px 0 10px 0;
    font-size: 14px;
    display: flex;
    .blue_text {
      color: #2e68ff;
    }
  }
  .explain_box {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    font-size: 13px;
    flex-wrap: wrap;

    .explain {
      color: #161f30;
      font-weight: 500;
      line-height: 26px;
    }
    .content {
      color: #161f30;
      line-height: 26px;
      flex: 1;
      word-break: break-all;
    }
    .star {
      // margin-left: auto;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-top: 5px;
      image {
        width: 12px;
        height: 12px;
      }
      image:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
