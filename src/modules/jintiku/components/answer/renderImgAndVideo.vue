<template>
  <div class="img-and-video">
    <div class="position-img" v-if="stem.resource_info.img">
      <!-- 底图 -->
      <div v-if="stem.resource_info.img.path">
        <van-image
          class="bg-img"
          :src="stem.resource_info.img.path"
          mode="widthFix"
        />
      </div>
      <!-- 坐标 -->
      <div
        class="position"
        v-for="(info, index) in stem.resource_info.data"
        :key="index"
        :style="{
          width: getCoord(info.coord).width,
          height: getCoord(info.coord).height,
          left: getCoord(info.coord).left,
          top: getCoord(info.coord).top,
        }"
        @click="playAudio(info.file, index)"
      >
        <van-image
          width="25px"
          height="25px"
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/17098008382958704170980083829567047_%E6%89%AC%E5%A3%B0%E5%99%A8%20(1).png"
        />
        <div
          class="ripple-effect"
          v-if="playIng && currentIndex == index"
        ></div>
      </div>
    </div>
    <div class="video" v-if="stem.resource_info.video">
      <video
        id="myVideo"
        class="myVideo"
        controls
        :src="completepath(stem.resource_info.video)"
        @error="videoErrorCallback"
      ></video>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    stem: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      currentIndex: 0,
      playIng: false,
    }
  },
  mounted() {
    this.initVideo()
  },
  methods: {
    initVideo() {
      // this.videoContext = uni.createVideoContext("myVideo", this)
    },
    completePath(path) {
      return env.VITE_APP_BASEOSSURL + path
    },
    // completepath(url) {
    //   console.log("urlurlurl", url)
    //   const env = useRuntimeConfig().public
    //   console.log(env.VITE_APP_BASEOSSURL + url)
    //   return env.VITE_APP_BASEOSSURL + url
    //   // return "http://jinyingjietest.oss-cn-beijing.aliyuncs.com/399390350378028349/2024/03/11/17101486764585770-1710148676458-45231.mp4"
    // },
    getCoord(coord) {
      if (coord) {
        let result = coord.split(",")
        return {
          width: result[0] * 100 + "%",
          height: result[1] * 100 + "%",
          left: result[2] * 100 + "%",
          top: result[3] * 100 + "%",
        }
      }
      return {
        width: 0,
        height: 0,
        left: 0,
        top: 0,
      }
    },
    playAudio(url, index) {
      this.currentIndex = index
      showToast("测试音频播放")
      const new_url = this.completepath(url)
      // const new_url =
      //   "http://jinyingjietest.oss-cn-beijing.aliyuncs.com/399390350378028349/2024/03/11/1710148488670658a-1710148488670-04136.mp3"
      console.log(new_url)
      // showToast("音频播放中")
      const audio = ref(new Audio(new_url))
      audio.value.play()
      // this.playIng = true
      // console.log(url)
      // this.$xh.playAudio(
      //   this.$xh.completepath(url),
      //   () => {
      //     this.playIng = false
      //   },
      //   () => {
      //     this.$xh.Toast("音频播放失败！")
      //     this.playIng = false
      //   }
      // )
    },
    videoErrorCallback() {
      // this.$xh.Toast("视频地址错误！")
      showToast("视频地址错误！")
    },
    stopPlay() {
      // this.$xh.pauseAudio()
      // this.videoContext.pause()
      this.playIng = false
    },
  },
  // destroyed() {
  //   console.log(1)
  //   this.$xh.pauseAudio()
  // }
}
</script>
<style lang="scss" scoped>
.img-and-video {
  width: 100%;
  position: relative;
  .bg-img {
    width: 100%;
    height: auto;
  }
  .position {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    image {
      width: 25px;
      height: 25px;
    }
  }
  .video {
    width: 100%;
    .myVideo {
      width: 100%;
    }
  }
}
.ripple-effect {
  position: absolute;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation-name: ripple-animate;
  animation-duration: 1s;
  animation-timing-function: ease-out;
  animation-iteration-count: infinite; /* 设置为无限循环播放 */
}
@keyframes ripple-animate {
  to {
    transform: scale(2.5);
    opacity: 0;
  }
}
</style>
