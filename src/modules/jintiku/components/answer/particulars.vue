<template>
  <div class="answer_particulars">
    <div v-for="(item, index) in data" :key="index">
      <!-- 每题${item.sum_score / item.question_num}分 -->
      <div class="headline">
        {{
          `${formatNumber(parseInt(index + 1))}、${item.question_type_name}
        ${
          item.question_type != "8" &&
          item.question_type != "9" &&
          item.question_type != "10" &&
          item.question_type != "11"
            ? "题"
            : ""
        }（共
        ${item.question_num}题），共
        ${item.sum_score}分`
        }}
      </div>
      <div
        v-for="(citem, cindex) in item.question_list"
        style="margin-top: 12px"
        :key="cindex"
      >
        <ModuleAnswerQuestionTmp
          :data="citem"
          :isCorrect="isCorrect"
          :question_sort="cindex"
        />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "answer-particulars", // 答题详情
  props: {
    data: {},
    isCorrect: {
      type: String,
    },
  },
  data() {
    return {}
  },
  methods: {
    formatNumber(num) {
      let number = Math.floor(num)
      const chineseNumbers = [
        "零",
        "一",
        "二",
        "三",
        "四",
        "五",
        "六",
        "七",
        "八",
        "九",
        "十",
      ]
      if (num <= 10) {
        return chineseNumbers[num]
      } else if (num > 10 && num < 100) {
        let unit = num % 10
        let ten = "十"
        let decade = Math.floor(num / 10)
        return `${chineseNumbers[decade]}${ten}${
          unit == 0 ? "" : chineseNumbers[unit]
        }`
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.answer_particulars {
  .headline {
    font-size: 14px;
    color: #161f30;
    margin-top: 22px;
  }
}
</style>
