<!-- 组件内容 -->
<template>
  <clientOnly>
    <view class="audio-page">
      <view class="box-left">
        <view class="page-btn" @click="clickAudio">
          <van-image
            :src="music_play ? stop_img : start_img"
            mode="widthFix"
          ></van-image>
        </view>
      </view>
      <view class="box-content">
        <view class="progress">
          <text>{{ getMinuteTime(now_time) }}</text>
          <slider
            :value="(now_time / total_time) * 100"
            block-size="10"
            block-color="#2e68ff"
            activeColor="#2e68ff"
            @change="sliderChange"
          ></slider>
          <text>{{ getMinuteTime(total_time) }}</text>
        </view>
      </view>
    </view>
  </clientOnly>
</template>
<script>
export default {
  name: "WZSAudio",
  props: ["music", "image", "title"],
  data() {
    return {
      music_play: false,
      AUDIO: "",
      total_time: 0,
      now_time: 0,
      timeupdata: "",
      interval: "",
      start_img:
        "data:image/png;base64,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",
      stop_img:
        "data:image/png;base64,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",
      intervalID: null,
    }
  },
  watch: {
    music: {
      handler(nv, ov) {
        this.cleanMusic()
        this.playAudio()
      },
      immediate: true,
    },
  },
  methods: {
    // 播放音乐
    playAudio() {
      // this.AUDIO = uni.createInnerAudioContext()
      // #ifdef H5
      // this.AUDIO = uni.createInnerAudioContext()
      // #endif
      // #ifdef APP
      // this.AUDIO =
      //   uni.getSystemInfoSync().platform == 'ios'
      //     ? uni.getBackgroundAudioManager()
      //     : uni.createInnerAudioContext()
      // #endif
      // this.AUDIO.src = this.music
      console.log(this.music)
      // this.AUDIO.onCanplay(() => {
      //   this.intervalID = setInterval(() => {
      //     console.log("定时器", this.AUDIO.duration)
      //     if (this.AUDIO.duration !== 0) {
      //       clearInterval(this.intervalID) // 清除定时器
      //       this.intervalID = null
      //       if (this.AUDIO.duration != 0 && !isNaN(this.AUDIO.duration)) {
      //         this.total_time = Math.round(this.AUDIO.duration)
      //         clearInterval(this.interval)
      //       }
      //     }
      //   }, 500)
      //   console.log(this.AUDIO.duration) // =>0
      // })
      // this.AUDIO.onError((res) => {
      //   console.log(res.errMsg)
      //   console.log(res.errCode)
      // })
      // this.timeupdata = setInterval(() => {
      //   console.log("定时器")
      //   if (this.music_play) {
      //     this.now_time++
      //     if (this.now_time >= this.total_time) {
      //       this.music_play = false
      //       this.now_time = 0
      //     }
      //   }
      // }, 1000)
      // this.AUDIO.play()
      // setTimeout(() => {
      //   this.AUDIO.pause()
      // }, 0)
    },
    clickAudio() {
      if (this.music_play) {
        this.music_play = false
        this.AUDIO.pause()
      } else {
        this.music_play = true
        this.AUDIO.play()
      }
    },
    // 拖动进度条
    sliderChange(e) {
      const second = (e.detail.value / 100) * this.total_time
      this.AUDIO.seek(second)
      this.now_time = second
    },
    // 秒转换分秒
    getMinuteTime(data) {
      let minute = parseInt(data / 60)
      let second = parseInt(data % 60)
      if (minute.toString().length == 1) minute = `0${minute}`
      if (second.toString().length == 1) second = `0${second}`
      return `${minute}:${second}`
    },
    cleanMusic() {
      if (this.music_play) {
        this.music_play = false
        this.AUDIO.pause()
      }
      this.now_time = 0
      this.timeupdata && clearInterval(this.timeupdata)
      this.AUDIO && this.AUDIO.destroy()
    },
  },
  destroyed() {
    this.cleanMusic()
    this.intervalID && clearInterval(this.intervalID)
  },
}
</script>
<style lang="scss" scoped>
.audio-page {
  width: 100%;
  height: 10.6vmin;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0.4vmin 0.4vmin 0.8vmin #ccc;

  .box-left {
    width: 10%;
    position: relative;
    display: flex;

    .box-img {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 2;
    }

    .page-btn {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 3;

      image {
        width: 6.6vmin;
        height: 6.6vmin;
        background-color: rgba($color: #000000, $alpha: 0.3);
        border-radius: 50%;
      }
    }
  }

  .box-content {
    width: 90%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 4vmin;
    box-sizing: border-box;
    font-size: 3.2vmin;

    .content-name {
      width: 100%;
      display: -webkit-box;
      /* 弹性盒模型 */
      -webkit-box-orient: vertical;
      /* 元素垂直居中*/
      -webkit-line-clamp: 1;
      /*  文字显示的行数*/
      overflow: hidden;
      /* 超出隐藏 */
    }

    .progress {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      slider {
        width: 80%;
      }
    }
  }
}
</style>
