<template>
  <div style="padding-top: 35px">
    <div class="title">答题卡</div>
    <div class="bar">
      <div class="yes">
        <div class="flag"></div>
        <div class="text">已做</div>
      </div>
      <div class="no">
        <div class="flag"></div>
        <div class="text">未做</div>
      </div>
      <div class="doubt">
        <div class="flag"></div>
        <div class="text">标疑</div>
      </div>
    </div>
    <div class="questions">
      <div
        class="question flex-center"
        :class="{
          done: item.user_option != '' || item.stem_list[0].selected.length,
          doubt: item.doubt,
        }"
        v-for="(item, index) in questions"
        :key="index"
        @click="change(index)"
      >
        {{ index + 1 }}
      </div>
    </div>
  </div>
</template>
<script>
// import pickerShell from '../commen/picker-shell.vue'
export default {
  // 答题卡查看
  name: "answer-sheet",
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    questions: {
      default: () => [],
    },
  },
  // components: {
  //   pickerShell
  // },
  data() {
    return {
      list: [],
    }
  },
  created() {
    console.log(1111111, this.sheetShow)
  },
  methods: {
    input(val) {
      this.$emit("input", val)
    },
    change(index) {
      this.$emit("change", index)
    },
  },
}
</script>
<style lang="scss" scoped>
.bar {
  display: flex;
  align-items: center;
  justify-content: center;
  .yes,
  .no,
  .doubt {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    .flag {
      width: 8px;
      height: 8px;
      background-color: #567dfa;
      margin-right: 6px;
      border-radius: 50%;
    }
    .text {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
    }
  }
  .no {
    margin-left: 47px;
    .flag {
      background-color: #e4e4e4;
    }
  }
  .doubt {
    margin-left: 47px;
    .flag {
      background-color: #fb9e0c;
    }
  }
}
.questions {
  padding: 30px 20px 25px;
  padding-bottom: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .question {
    width: 40px;
    height: 40px;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #000000;
    border-radius: 50%;
    background-color: #f6f6f6;
    margin-right: 32px;
    margin-bottom: 22px;
    &:nth-child(5n) {
      margin-right: 0;
    }
  }
  .question.done {
    background: #e7f3fe;
    color: #567dfa;
  }
  .question.doubt {
    background-color: #fb9e0c;
    color: #ffffff;
  }
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.title {
  height: 44px;
  line-height: 59px;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: #03203d;
  text-align: center;
  font-weight: 800;
  position: absolute;
  left: 0;
  top: 0;
  text-align: center;
  width: 100%;
}
</style>
