<template>
  <clientOnly>
    <van-swipe
      class="swiper"
      :initial-swipe="current"
      :duration="300"
      :circular="false"
      :touchable="false"
      :show-indicators="false"
      :loop="false"
      @change="swiperChange"
    >
      <van-swipe-item v-for="(item, index) in lists" :key="index">
        <div
          class="swiper-item"
          v-if="
            index == current || index == current - 1 || index == current + 1
          "
        >
          <div class="select-question-box">
            <ModuleAnswerSelectQuestion
              v-for="(jtem, j) in item.stem_list"
              :key="j"
              :info="item"
              :stem="jtem"
              @selecte="selecte"
              @input="inputChange"
              :answer="false"
              :current="current"
            />
          </div>
        </div>
      </van-swipe-item>
    </van-swipe>
  </clientOnly>
</template>
<script>
export default {
  components: {},
  props: {
    indicatorDots: {
      type: Boolean,
      default: false,
    },
    autoplay: {
      type: Boolean,
      default: false,
    },
    lists: {
      type: Array,
      default: [],
    },
    index: {
      type: Number,
      default: 0,
    },
    disableTouch: {
      type: Boolean,
      default: false,
    },
    skipHiddenItemLayout: {
      type: <PERSON>olean,
      default: false,
    },
  },
  data() {
    return {
      current: 0,
    }
  },
  methods: {
    prev() {
      if (this.current <= 0) {
        showToast("已经是第一题了哦！")
        return
      }
      this.current--
      this.$emit("index", this.current)
    },
    next() {
      if (this.current >= this.lists.length - 1) {
        // showToast("已经是最后一题了哦！")
        this.$emit("last")
        return
      }
      this.current++
      console.log("current", this.current)
      this.$emit("index", this.current)
    },
    swiperChange(e) {
      console.log("eeeeeee", e)
      this.current = e
      this.$emit("index", this.current)
    },
    selecte(info) {
      console.log("info", info)
      let isNext = false
      let data = this.lists.map((res) => {
        if (res.sub_question_id == info.sub_question_id) {
          isNext = info.stem_list[0].multiple
          return { ...info, doubt: false }
        }
        return res
      })
      this.$emit("lists", data)
      if (!isNext && this.isSelectedType(info.type)) {
        // 选择题才往下一题走
        this.next()
      }
    },
    inputChange(info) {
      let data = this.lists.map((res) => {
        if (res.sub_question_id == info.sub_question_id) {
          return { ...info, doubt: false }
        }
        return res
      })
      this.$emit("lists", data)
    },
    isSelectedType(type) {
      return !this.isFillBlanks(type) && !this.isSubjective(type)
    },
    isSubjective(type) {
      return type == "8" || type == "10" || type == "14" // 14案例题
    },
    isFillBlanks(type) {
      return type == "9"
    },
  },
  watch: {
    index: {
      handler(val) {
        console.log("valvalvalval", val)
        this.current = val
      },
      immediate: true,
    },
  },
  mounted() {
    console.log("lists", this.lists)
  },
}
</script>
<style lang="scss" scoped>
.swiper {
  height: 100%;
  .swiper-item {
    overflow-y: auto;
    padding: 24px 19px;
    height: 100%;
    background-color: #fff;
    .question-answer-box {
      margin-top: 43px;
    }
    .answer {
      margin-top: 30px;
    }
  }
}
</style>
