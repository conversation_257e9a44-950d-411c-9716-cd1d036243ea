<template>
  <div
    class="select-lesson mask"
    :class="{ show }"
    @click="close"
    :style="{ height: show ? '100%' : '0', top: top + 'rpx' }"
  >
    <!-- :style="{ height: isShow ? '100%' : '0' }" -->
    <div class="box" :class="{ show }" @click.stop="() => {}">
      <div class="title">选择对应校区下的开课城市</div>
      <div class="select-content">
        <div class="select-content-left">
          <div
            class="select-menu hide-text"
            :class="{ active: leftActiveId == item.id }"
            v-for="(item, index) in dataList"
            :key="index"
            @click.stop="getRightInfo(item)"
          >
            {{ item.data_name }}
          </div>
        </div>
        <div class="select-content-right">
          <div class="select-menus" v-for="(res, j) in rightInfos" :key="j">
            <div class="select-menus-name hide-text">
              {{ res.data_name }}
            </div>
            <div class="select-menus-boxs" v-if="res.subs && res.subs.length">
              <div
                class="select-menu-item hide-text"
                v-for="(item, index) in res.subs"
                :class="{ active: item.id == value }"
                :key="index"
                @click.stop="change(item, res)"
              >
                {{ item.data_name }}
              </div>
            </div>
            <div class="select-menus-boxs" v-else>
              <div
                class="select-menu-item hide-text"
                :class="{ active: res.id == value }"
                @click.stop="change(res)"
              >
                {{ res.data_name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import { getMajor, checkMajor } from '../api/index'
export default {
  props: {
    value: {
      type: String,
    },
    show: {
      type: Boolean,
    },
    citymajorName: {
      type: String,
    },
    top: {
      type: Number,
      default: 176,
    },
  },
  data() {
    return {
      leftActiveId: "",
      datas: [],
      dataList: [],
      rightInfos: [],
      isShow: false,
      parentInfo: {},
    }
  },
  methods: {
    startFun() {
      let obj = {
        code: "project,major,subject",
        is_auth: 2,
        is_usable: 1,
        professional_ids: "",
      }
      // getMajor(obj).then(res => {
      //   this.dataList = res.data
      // })
    },
    getRightInfo(item) {
      this.leftActiveId = item.id
      this.rightInfos = item.subs
      this.parentInfo = item
    },
    change(item, secondParent) {
      // let token = uni.getStorageSync("__xingyun_token__")
      // if (token) {
      //   let { student_id = '' } = uni.getStorageSync('__xingyun_userinfo__')
      //   checkMajor({
      //     id: student_id,
      //     major_id: item.id
      //   }).then(res => {
      //     this.$xh.Toast('操作成功')
      //     try {
      //       let obj = {
      //         major_id: item.id,
      //         major_name: item.data_name,
      //         parent_major_name: secondParent
      //           ? secondParent.data_name + '-' + this.parentInfo.data_name
      //           : this.parentInfo.data_name
      //       }
      //       uni.setStorageSync('__xingyun_major__', obj)
      //       uni.setStorageSync(
      //         'parent_major_name',
      //         secondParent
      //           ? secondParent.data_name + '-' + this.parentInfo.data_name
      //           : this.parentInfo.data_name
      //       )
      //     } catch (error) {}
      //     this.$emit('input', item.id)
      //     this.$emit('change', item)
      //     this.$emit('update:show', false)
      //     this.$emit('update:citymajorName', item.data_name)
      //   })
      //   return
      // }
      // 游客模式
      this.$xh.Toast("操作成功")
      try {
        let obj = {
          major_id: item.id,
          major_name: item.data_name,
          parent_major_name: this.parentInfo.data_name,
          parent_major_name: secondParent
            ? secondParent.data_name + "-" + this.parentInfo.data_name
            : this.parentInfo.data_name,
        }
        // uni.setStorageSync("__xingyun_major__", obj)
        // uni.setStorageSync(
        //   "parent_major_name",
        //   secondParent
        //     ? secondParent.data_name + "-" + this.parentInfo.data_name
        //     : this.parentInfo.data_name
        // )
      } catch (error) {}
      this.$emit("input", item.id)
      this.$emit("change", item)
      this.$emit("update:show", false)
      this.$emit("update:citymajorName", item.data_name)
    },
    close() {
      this.$emit("update:show", false)
    },
  },
  watch: {
    show(value) {
      if (value) {
        this.isShow = true
        this.startFun()
      } else {
        setTimeout(() => {
          this.isShow = false
        }, 250)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.mask {
  position: fixed;
  left: 0;
  // top: 176rpx;
  width: 100%;
  height: 100%;
  transition: all 0.25s;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0;
  // pointer-events: none;
  .box {
    background-color: #fff;
    height: 0;
    transition: all 0.25s;
    overflow: hidden;
    // height: 1032rpx;
    .title {
      height: 72rpx;
      display: flex;
      align-items: center;
      padding-left: 24rpx;
      margin-bottom: 24rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(3, 32, 61, 0.65);
    }
    .select-content {
      height: 960rpx;
      display: flex;
      .select-content-left {
        width: 184rpx;
        background: #f6f7f8;
        overflow-y: auto;
        padding-bottom: 30rpx;
        .select-menu {
          height: 96rpx;
          background: #f6f7f8;
          width: 100%;
          text-align: center;
          line-height: 96rpx;
          padding: 0 10rpx;
          // display: flex;
          // align-items: center;
          // justify-content: center;
          font-size: 26rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #03203d;
        }
        .select-menu.active {
          background-color: #fff;
        }
      }
      .select-content-right {
        flex: 1;
        background-color: #fff;
        padding: 0 24rpx;
        // display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        overflow-y: auto;
        height: 100%;
        .select-menus {
          .select-menus-name {
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(3, 32, 61, 0.65);
            margin-bottom: 24rpx;
          }
          .select-menus-boxs {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            justify-content: space-between;
            .select-menu-item {
              border: 2rpx solid transparent;
              // width: 224rpx;
              width: calc(50% - 11rpx);
              height: 68rpx;
              line-height: 68rpx;
              text-align: center;
              // display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 34rpx;
              background: #f6f7f8;
              color: #03203d;
              font-size: 24rpx;
              transition: all 0.25s;
              margin-bottom: 32rpx;
              padding: 0 10rpx;
              // margin-right: 22rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            // .select-menu-item:nth-child(2n) {
            //   margin-right: 0;
            // }
            .select-menu-item.active {
              background: #ebf1ff;
              border: 2rpx solid rgba(46, 104, 255, 0.5);
              color: #2e68ff;
            }
          }
        }
      }
    }
  }
  .show {
    height: 1032rpx;
  }
}
.show {
  opacity: 1;
}
</style>
