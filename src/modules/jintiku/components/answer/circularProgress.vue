<template>
  <div class="main-contain">
    <canvas id="canvas" width="260" height="128"></canvas>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  num: {
    type: Number,
    default: 0,
  },
})

let speed = 0
let i = 0
let timer: any
let num = props?.num //Math.random()
var dom: HTMLElement //获取画布的上一级dom元素对象
var canvas: HTMLCanvasElement //获取画布承载容器,不加HTMLCanvasElement会报错
/* 画曲线 */
function drawCircle(ctx: any, config: any) {
  const { x, y, radius, startAngle, endAngle, color, lineWidth, type } = config
  ctx.beginPath()
  ctx.arc(x, y, radius, startAngle, endAngle, false)
  // 设定曲线粗细度
  ctx.lineWidth = lineWidth
  if (type == 1) {
    var jb = ctx.createLinearGradient(0, 0, 300, 0)
    // jb.addColorStop(0, "#D3FFEF") //参数为0<stop<1,color
    jb.addColorStop(1, "#01A363")
    // 给曲线着色
    ctx.strokeStyle = jb
  } else {
    // 给曲线着色
    ctx.strokeStyle = color
  }

  // 连接处样式
  ctx.lineCap = "round"
  // 给环着色
  ctx.stroke()
  ctx.closePath()
}

// 画指示标
function drawZsb(ctx: any, config: any) {
  const { x, y, radius, startAngle, endAngle, color, lineWidth, type } = config
  // console.log(x, y)
  ctx.beginPath()
  ctx.arc(x, y, 10, 0, 2 * Math.PI)
  ctx.fillStyle = "#fff"
  ctx.strokeStyle = "#fff" // 外圈线颜色也为白色
  ctx.lineWidth = 2 // 设置线宽，增加外圈线的宽度
  ctx.strokeStyle = color
  ctx.fill()
  ctx.stroke()
}

function circle(percent: any = 0) {
  const { width, height } = canvas
  const ctx = canvas.getContext("2d")! //加上!告诉ts我有信心它不为空
  // // 清除画布
  ctx.clearRect(0, 0, width, height)
  // // 保存
  // ctx.save();
  // ctx.globalAlpha = speed / num;
  /* 填充文字 */
  // ctx.font = "24px Microsoft YaHei"
  // /* 文字颜色 */
  // // var jb = ctx.createLinearGradient(0, 0, 200, 0)
  // // jb.addColorStop(0, "blue") //参数为0<stop<1,color
  // // jb.addColorStop(1, "red")
  // // ctx.fillStyle = jb
  // /* 文字内容 */
  // const insertContent = ""
  // // 拿到文本内容的像素相关信息 单位长度(px)
  // const measureText = ctx.measureText(insertContent)
  // /* 插入文字，后面两个参数为文字在画布中的坐标点 */
  // /* 此处注意：text.width获得文字的宽度，然后就能计算出文字居中需要的x值 */
  // ctx.fillText(insertContent, (width - measureText.width) / 2, height / 2 + 45)

  /* 填充百分比 */
  ctx.font = "64px Microsoft YaHei"
  ctx.fillStyle = "#262629"
  const ratioStr = `${(parseFloat(percent) * 100).toFixed(0)}`
  const text = ctx.measureText(ratioStr)
  ctx.fillText(ratioStr, (width - text.width - 20) / 2, 355)

  const percent_t = "%"
  ctx.font = "40px Microsoft YaHei"
  ctx.fillStyle = "#262629"
  const percentText = ctx.measureText(percent_t)
  ctx.fillText(percent_t, (width - percentText.width + 115) / 2, 355)

  const font_t = "正确率"
  ctx.font = "28px Microsoft YaHei"
  ctx.fillStyle = "#93969F"
  const fontText = ctx.measureText(font_t)
  ctx.fillText(font_t, (width - fontText.width) / 2, 395)

  /* 开始圆环 */
  const circleConfig = {
    /* 圆心坐标 */
    x: width / 2,
    y: 392,
    /* 半径，下方出现的150都是半径 */
    radius: width / 2 - 60,
    /* 环的宽度 */
    lineWidth: 30,
    /* 开始的度数-从上一个结束的位置开始 */
    // startAngle: 0, // 注意这里的0是3点钟方向，而非12点方向，和数学里的不一样
    /* 结束的度数 */
    startAngle: (Math.PI / 180) * 180,
    endAngle: (Math.PI / 180) * 0,
    color: "#D3FFEF",
    type: 0,
  }
  /* 灰色的圆环 */
  drawCircle(ctx, circleConfig)

  const circleConfig2 = {
    ...circleConfig,
    startAngle: (Math.PI / 180) * 180, // 注意这里的0是3点钟方向，而非12点方向，和数学里的不一样
    /* 结束的度数 */
    endAngle: (Math.PI / 180) * 180 + (Math.PI * 2 * speed) / 2,
    type: 1,
  }
  /* 百分比的圆环 */
  drawCircle(ctx, circleConfig2)
  // 画指示标
  // const indicatorAngle = 0.75 + 1.5 * speed
  const indicatorAngle = speed * 1.5 + 0.74
  const indicatorRadius = width / 2 - 60
  const circleConfig3 = {
    // x: width / 2 + Math.cos(indicatorAngle * Math.PI * 0) * indicatorRadius,
    x: width / 2,
    y: 220 + Math.sin(indicatorAngle * Math.PI * 0) * indicatorRadius,
    color: "red",
  }
  // drawZsb(ctx, circleConfig3)

  timer = window.requestAnimationFrame(() => {
    //在1秒内完成一次的进度显示
    if (speed > num) {
      window.cancelAnimationFrame(timer)
      return
    }
    circle(speed)
  })
  // speed = 0.01 + speed
  speed = floatAdd(0.01, speed)
  console.log("speed", speed)
}

function floatAdd(a: number, b: number) {
  var c, d, e
  if (undefined == a || null == a || isNaN(a)) {
    a = 0
  }
  if (undefined == b || null == b || isNaN(b)) {
    b = 0
  }
  try {
    c = a.toString().split(".")[1].length
  } catch (f) {
    c = 0
  }
  try {
    d = b.toString().split(".")[1].length
  } catch (f) {
    d = 0
  }
  e = Math.pow(10, Math.max(c, d))
  return (floatMul(a, e) + floatMul(b, e)) / e
}

function floatMul(a: number, b: number) {
  var c = 0,
    d = a.toString(),
    e = b.toString()
  try {
    c += d.split(".")[1].length
  } catch (f) {}
  try {
    c += e.split(".")[1].length
  } catch (f) {}
  return (
    (Number(d.replace(".", "")) * Number(e.replace(".", ""))) / Math.pow(10, c)
  )
}

onMounted(() => {
  dom = document.querySelector(".main-contain")!
  canvas = document.querySelector("#canvas")!
  let dpr = window.devicePixelRatio
  canvas.style.width = canvas.width + "px"
  canvas.style.height = canvas.height + "px"
  let { width: cssWidth, height: cssHeight } = canvas.getBoundingClientRect()
  canvas.width = dpr * cssWidth
  canvas.height = dpr * cssHeight

  circle(speed)
})
</script>
<style scoped>
.main-contain {
  height: 100%;
  background: #fff;
}
</style>
