<template>
	<view class="tree-head" :style="{
      paddingLeft: 32 + (item.leval - 1) * 40 + 'rpx'
    }">
		<view class="head" @click="headClick(item)">
			<view class="left">
				<view class="image" @click.stop="$emit('expand')">
					<image
						src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950043427848e3c169500434278459705_select.png"
						mode="widthFix" :class="{
              isExpand
            }" />
				</view>
				<text class="tit">{{ item.sectionname }}</text>

				<subscript :key="item.id" :info="item" v-if="level > 1"></subscript>
			</view>
			<view class="right">
				<text>{{ item.do_question_num }}</text>
				<text>/{{ item.question_number }}</text>
				<image
					src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950046792662774169500467926615379_edit.png"
					mode="widthFix" class="edit" v-if="!disabled" />
			</view>
		</view>
	</view>
</template>
<script>
	// 展开头
	import subscript from './subscript.vue'
	export default {
		components: {
			subscript
		},
		inject: ['updatePrePriceData'],
		props: {
			item: {
				type: Object,
				default: () => ({})
			},
			isExpand: {
				type: Boolean
			},
			disabled: {
				type: Boolean,
				default: false
			},
			level: {
				type: Number,
				default: 1
			},
			type: {
				type: String,
				default: '1'
			},
			isfree: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {}
		},
		methods: {
			headClick(item) {
				if(item.leval < 3){
					this.$emit('expand')
				}else{
					this.goDetail(item);
				}
			},
			goDetail(item) {
				if (this.disabled) {
					return
				}
				if (item.question_number == '0') {
					this.$xh.Toast('暂无题目！')
					return
				}
				if (item.not_answered_question_num == '0') {
					// 跳转看题
					this.$xh.push(
						'jintiku',
						`pages/makeQuestion/lookAnalysisQuestion?disabled=${
            this.disabled
          }&knowledge_id=${item.id}&type=${this.type}&chapter_id=${
            item.sectionprent
          }&teaching_system_package_id=${
            item.teaching_system_package_id
          }&isnextChapter=${2}`
					)
					return
				}
				// 先把其他消除
				// this.setDataInfo(this.lists)
				// item.isPreCurrent = true
				// item.is_checked = '1'
				this.updatePrePriceData(item.id)
				this.$xh.push(
					'jintiku',
					`pages/makeQuestion/makeQuestion?disabled=${
          this.disabled
        }&knowledge_id=${item.id}&type=${this.type}&chapter_id=${
          item.sectionprent
        }&teaching_system_package_id=${
          item.teaching_system_package_id
        }&isnextChapter=${2}&isfree=${this.isfree ? 1 : 0}&professional_id=${
          item.professional_id
        }`
				)
			}
		}
	}
</script>
<style scoped lang="less">
	.tree-head {
		padding: 0 32rpx;

		.head {
			height: 124rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1rpx solid #f0f0f0;

			.left {
				display: flex;
				align-items: center;

				.image {
					height: 124rpx;
					display: flex;
					align-items: center;
				}

				image {
					width: 32rpx;
					height: 32rpx;
					margin-right: 24rpx;
					transition: all 0.25s;

					&.isExpand {
						transform: rotateZ(180deg);
					}
				}

				text {
					font-size: 28rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 800;
					color: #333333;
				}

				.tit {
					margin-right: 4rpx;
				}
			}

			.right {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
				display: flex;
				align-items: center;

				text:first-child {
					color: #387dfc;
				}

				.edit {
					margin-left: 8rpx;
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
	}
</style>