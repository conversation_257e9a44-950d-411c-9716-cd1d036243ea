<template>
	<view class="study-calendar">
		<!-- 右上角装饰图片 -->
		<image class="decoration-image"
			src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/study-card-color.png" />

		<view class="check-in-status">
			<image class="check-icon" src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/study-card-zan.png"
				mode="aspectFit" />
			<text class="check-text">{{ isCheckedIn?"今日已打卡":"今日未打卡"}}</text>
		</view>

		<view class="calendar-header">
			<text class="calendar-title">学习日历</text>
		</view>

		<view class="calendar-content">
			<view class="stats-row">
				<view class="stat-item">
					<text class="stat-number">{{ persistDays?persistDays:'0' }}</text>
					<text class="stat-label">累计坚持天数</text>
				</view>

				<view class="stat-item">
					<text class="stat-number">{{carndownTime()}}</text>
					<text class="stat-label">考试倒计时天数</text>
				</view>

				<view class="stat-item end-item">
					<view class="item-left">
						<text class="thrid-q">做题数:{{ totalQuestions?totalQuestions:0 }}</text>
						<text class="stat-label">正确率:{{ accuracyRate?accuracyRate:0 }}%</text>
					</view>
				</view>
			</view>
			<login>
				<view class="action-section">
					<view class="check-in-btn" :class="{ 'checked': hasLogin() }" @click="handleCheckIn">
						<text :class="hasLogin()?'btn-text-checked':'btn-text'">{{hasLogin()?'已打卡':'打卡'}}</text>
					</view>
				</view>
			</login>

		</view>
	</view>
</template>

<script>
	import login from './commen/login'
	import {
		isLogin
	} from '../utils/index.js'
	export default {
		components: {
			login
		},
		name: 'StudyCalendar',
		props: {
			// 坚持天数
			persistDays: {
				type: [Number, String],
				default: 31
			},
			// 做题总数
			totalQuestions: {
				type: [Number, String],
				default: 700
			},
			// 正确率
			accuracyRate: {
				type: [Number, String],
				default: 12
			},
			// 是否已打卡
			isCheckedIn: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				ischecked: false
			}
		},
		methods: {
			hasLogin() {
				if (!isLogin()) {
					return false
				}
				return this.isCheckedIn
			},
			handleCheckIn() {
				if (!this.isCheckedIn) {
					this.$emit('check-in')
				}
			},
			carndownTime() {
				const today = new Date();
				const currentYear = today.getFullYear();

				// 创建今年的8月22日
				let targetDate = new Date(currentYear, 7, 22); // 月份从0开始，7表示8月

				// 如果今年的8月22日已经过了，就设置为下一年的8月22日
				if (today > targetDate) {
					targetDate = new Date(currentYear + 1, 7, 22);
				}

				// 计算天数差
				const timeDiff = targetDate.getTime() - today.getTime();
				const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

				return daysDiff.toString();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.study-calendar {
		font-family: PingFangSC-Medium, PingFang SC;
		background: white;
		border-radius: 32rpx;
		padding: 32rpx 40rpx;
		box-shadow: 0 4rpx 20rpx rgba(46, 104, 255, 0.1);
		position: relative;
		overflow: hidden;

		// 右上角装饰图片
		.decoration-image {
			position: absolute;
			top: 0;
			right: 0;
			width: 260rpx;
			height: 64rpx;
			opacity: 0.8;
		}

		.check-in-status {
			display: flex;
			align-items: center;
			justify-content: center;
			position: absolute;
			top: 0;
			right: 0;
			width: 260rpx;
			height: 64rpx;

			.check-icon {
				width: 32rpx;
				height: 32rpx;
				margin-right: 8rpx;
			}

			.check-text {
				font-size: 24rpx;
				font-weight: 500;
			}
		}

		.calendar-header {
			margin-bottom: 32rpx;
			position: relative;
			z-index: 2;
			display: flex;
			align-items: center;

			.calendar-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				line-height: 1.2;
			}

		}

		.calendar-content {
			position: relative;
			z-index: 2;
			display: flex;
			flex-direction: column;


			.stats-row {
				display: flex;
				align-items: flex-end;
				justify-content: space-between;

				.stat-item {
					text-align: center;
					width: 33%;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					.end-item {
						align-items: flex-end;
					}

					.item-left {
						display: flex;
						flex-direction: column;
						align-items: flex-start;
					}


					.stat-number {
						display: block;
						font-size: 40rpx;
						font-weight: 700;
						line-height: 1.2;
						margin-bottom: 8rpx;
					}

					.thrid-q {
						display: block;
						font-size: 28rpx;
						line-height: 1.2;
						color: #666;
						margin-bottom: 8rpx;
					}

					.stat-label {
						display: block;
						font-size: 26rpx;
						color: #666;
						line-height: 1.2;
						margin-top: 14rpx;
					}
				}
			}

			.action-section {
				margin-top: 32rpx;

				.check-in-btn {
					background: #FF5500; //linear-gradient(135deg, #FF860E 0%, #FF6912 100%);
					border-radius: 16rpx;
					width: 100%;
					height: 70rpx;
					// box-shadow: 0 4rpx 16rpx rgba(255, 138, 0, 0.3);
					transition: all 0.3s ease;
					display: flex;
					flex-direction: row;
					justify-content: center;
					align-items: center;

					&:active {
						transform: scale(0.95);
					}

					&.checked {
						background: #FFEEE7;
						// box-shadow: 0 4rpx 16rpx #E0F0FF;
					}

					.btn-text {
						font-size: 28rpx;
						color: #fff;
					}

					.btn-text-checked {
						font-size: 28rpx;
						color: #F44900;
					}
				}
			}
		}
	}

	// 响应式适配
	@media screen and (max-width: 750rpx) {
		.calendar-header {
			margin-bottom: 24rpx;

			.title-section {
				.calendar-title {
					font-size: 32rpx;
				}

				.check-in-status {
					.check-icon {
						width: 28rpx;
						height: 28rpx;
					}

					.check-text {
						font-size: 22rpx;
					}
				}
			}
		}

		.study-calendar {
			margin: 16rpx;
			padding: 24rpx;

			.calendar-content {
				.stats-row {
					margin-bottom: 20rpx;

					.stat-item {
						.stat-number {
							font-size: 40rpx;
						}

						.stat-label {
							font-size: 22rpx;
						}
					}
				}

				.action-section {
					.check-in-btn {

						.btn-text {
							font-size: 26rpx;
						}
					}
				}
			}
		}
	}
</style>