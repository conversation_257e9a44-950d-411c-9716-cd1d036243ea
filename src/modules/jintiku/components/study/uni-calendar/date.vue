<template>
  <view class="date">
    <view @click="showPicker">
      <slot name="default" />
    </view>
    <u-popup :show="show" mode="bottom" @close="show = false">
      <u-picker
          ref="picker"
          :columns="columns"
          @cancel="show = false"
          @confirm="onConfirm"
      />
    </u-popup>
  </view>
</template>
<script>
export default {
  name: 'ModuleStudyUniCalendarDate',
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      columns: []
    }
  },
  mounted() {
    this.generateColumns()
  },
  methods: {
    generateYears() {
      const currentYear = new Date().getFullYear();
      const years = [];
      for (let year = currentYear - 1; year <= currentYear + 1; year++) {
        years.push({text: year.toString() + '年', value: year.toString()});
      }
      return years;
    },

    generateMonths() {
      const months = [];
      for (let month = 1; month <= 12; month++) {
        const monthText = month.toString() + '月'; // 去掉前导零
        const monthValue = month.toString().padStart(2, '0'); // 保持前导零
        months.push({text: monthText, value: monthValue});
      }
      return months;
    },

    generateColumns() {
      this.columns = [
        this.generateYears(),
        this.generateMonths()
      ];
    },

    showPicker() {
      this.show = true;
      // 可以在这里设置默认值
      // this.$refs.picker.setValues([this.value.split('-')[0], this.value.split('-')[1]])
    },

    onConfirm(val) {
      this.$emit('change', {detail:{value: val.selectedValues.join('-')}});
      this.show = false;
    }
  }
}
</script>
<style lang="less" scoped>

</style>
