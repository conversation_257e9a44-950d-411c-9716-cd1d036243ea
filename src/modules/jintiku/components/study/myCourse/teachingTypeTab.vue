<template>
  <view class="teaching-type-tab">
    <view
      class="teaching-type-item"
      v-for="item in teachingTypeOption"
      :key="item.id"
      @click="clickFun(item)"
    >
      <view
        class="teaching-type-item-name"
        :class="{ 'teaching-type-item-name-active': item.id === value }"
      >
        {{ item.name }}
      </view>
      <view class="teaching-type-item-line" v-if="item.id === value"></view>
    </view>
  </view>
</template>

<script>
export default {
  name: "ModuleStudyMyCourseTeachingTypeTab",
  props: {
    value: {
      type: String,
    },
  },
  data() {
    return {
      teachingTypeOption: [
        { name: "录播课", id: "3" },
        { name: "直播课", id: "1" },
        { name: "面授课", id: "2" },
      ],
    };
  },
  methods: {
    clickFun(item) {
      this.$emit("input", item.id);
      this.$emit("change", item.id);
    },
  },
};
</script>

<style scoped lang="less">
.teaching-type-tab {
  display: flex;
  justify-content: space-around;
  font-weight: 400;
  font-size: 14px;
  color: rgba(51, 51, 51, 0.85);
  padding-top: 16px;
  .teaching-type-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .teaching-type-item-name {
      margin-bottom: 3px;
    }
    .teaching-type-item-line {
      width: 22px;
      height: 3px;
      border-radius: 1.5px;
      background: #018CFF;
    }
    .teaching-type-item-name-active {
      font-weight: 600;
      font-size: 17px;
      color: #333333;
    }
  }
}
</style>
