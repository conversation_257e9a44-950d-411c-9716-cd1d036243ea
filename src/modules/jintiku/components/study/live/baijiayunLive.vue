<template>
  <view class="my-detail">
    <!-- uni-app中使用web-view组件替代iframe -->
    <web-view :src="url" class="live-webview"></web-view>
  </view>
</template>
<script>
export default {
  name: 'ModuleStudyLiveBaijiayunLive',
  data() {
    return {
      url: ''
    }
  },
  props:{
	  pageData:{
		  type:Object
	  }
  },
  mounted() {
    // 获取页面参数
    const query = this.pageData ? this.pageData : {};
	console.log(query, "query");
    this.url = decodeURIComponent(query.url || '');
	console.log(this.url, "this.url");
  }
}
</script>
<style lang="less" scoped>
.my-detail{
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  .live-webview{
    width: 100%;
    height: 100%;
  }
}
</style>
