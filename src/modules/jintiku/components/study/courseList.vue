<template>
  <view class="study-plan">
    <view id="studyPlanTitle" class="anchor"></view>
    <view class="study-plan-title">
      <img style="margin-right: 10rpx; width: 30rpx;height: 30rpx;"
      	src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/title-icon.png" alt="" />
      <text style="font-weight: 600">学习计划</text>
    </view>
    <view class="study-plan-search">
      <view class="study-plan-search-top">
        <view
          id="scroll-dom"
          class="select-teaching-type"
          @click="selectTeachingType"
        >
          <text style="font-weight: 400">{{
            teaching_type
              ? getTeachingTypeName(teaching_type)
              : "授课形式"
          }}</text>
        </view>
        <view class="my-course" @click="goMyCourse">
          <text style="color: #0075FF;">我的课程</text>
        </view>
      </view>
    </view>
    <ModuleStudyCourse v-if="list.length" :list="list" />
    <view v-if="loading" style="text-align: center; margin-top: 50px">
      <van-loading />
    </view>
    <view class="not-study-course" v-if="!list.length && !loading">
      <image
        class="empty-img"
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4045173295663081752515_8b3592c2dcddcac66af8ddd46abbbf1b74efa19fac63-AlBs3V_fw1200%402x.png"
      />
      <view class="empty-message">未找到符合的学习内容</view>
    </view>
  </view>
</template>
<script>
import ModuleStudyCourse from './course.vue'

export default {
  name: 'ModuleStudyCourseList',
  components: {
    ModuleStudyCourse
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    teaching_type: {
      type: String,
      default: "",
    },
    list: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      teachingTypeOption: [
        { name: "全部", id: "" },
        { name: "录播", id: "3" },
        { name: "直播", id: "1" },
        { name: "面授", id: "2" },
      ]
    }
  },
  methods: {
    getTeachingTypeName(id) {
      const item = this.teachingTypeOption.find(item => item.id == id);
      return item ? item.name : '';
    },
    selectTeachingType() {
      // 在uni-app中使用页面滚动
      uni.pageScrollTo({
        selector: '#studyPlanTitle',
        duration: 300
      });
      this.$emit("update:modelValue", true);
    },
    goMyCourse() {
      this.$xh.push('jintiku', 'pages/study/myCourse/index');
    }
  }
}
</script>
<style lang="less" scoped>
.study-plan {
  min-height: calc(100vh - 86px);
  position: relative;

  .anchor {
    position: absolute;
    top: -52px;
  }

  .study-plan-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-right: 15px;
    padding-top: 20px;
    padding-left: 15px;
    margin-bottom: 16px;
    font-weight: 600;
    font-size: 15px;
    color: #262629;

    .line {
      width: 4px;
      height: 16px;
      background: #018CFF;
      border-radius: 3px;
      margin-right: 6px;
    }
  }

  .study-plan-search {
    position: relative;

    .study-plan-search-top {
      padding-right: 15px;
      padding-left: 15px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;

      .select-teaching-type {
        font-weight: 500;
        font-size: 15px;
        color: #262629;

        .select-teaching-type-img {
          width: 6px;
          height: 6px;
          margin-left: 3px;
          transform: translateY(2px);
        }
      }

      .my-course {
        font-weight: 400;
        font-size: 14px;
        color: rgba(3, 32, 61, 0.75);
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .my-course-img {
          width: 14px;
          height: 14px;
          margin-right: 3px;
        }
      }
    }
  }

  .study-plan-list {
    padding: 40rpx 30rpx;

    .study-plan-item {
      background: #fff;
      margin-bottom: 40rpx;
      border-radius: 8rpx;
      padding: 30rpx;

      .study-plan-top {
        .business-type {
        }

        .study-plan-top-name {
        }
      }

      .study-plan-time {
      }

      .business-type {
      }

      .study-plan-teaching-list {
        display: flex;
        justify-content: flex-start;

        .study-plan-teaching {
          display: flex;
          justify-content: flex-start;
          margin-right: 20rpx;

          .teaching-img {
            width: 30rpx;
            height: 30rpx;
            margin-right: 16rpx;
          }

          .study-plan-teaching-message {
            .teaching-name {
            }

            .teaching-title {
              color: #ccc;
            }
          }
        }
      }

      .today-lesson-operation {
        display: flex;
        justify-content: flex-end;

        .appraise {
        }
      }
    }
  }
  .not-study-course {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    margin-top: 180px;

    .empty-img {
      width: 156px;
      height: 102px;
      margin-bottom: 4px;
    }

    .empty-message {
      font-weight: 400;
      font-size: 12px;
      color: rgba(3, 32, 61, 0.45);
      margin-bottom: 32px;
    }
  }
}
</style>
