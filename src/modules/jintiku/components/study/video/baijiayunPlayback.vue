<template>
  <view class="my-detail">
    <view class="video-box" v-for="(item, index) in url" :key="index">
      <web-view
        v-if="active == index + 1"
        :src="item"
        class="baijiayun-playback-webview"
      ></web-view>
    </view>
    <!--    <div class="tabs">-->
    <!--      <div v-for="item in options" :class="{active: active==item.value}" class="tabs-item"-->
    <!--           @click="changeSelected(item.value)">-->
    <!--        {{ item.text }}-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div style="padding: 0px 12px;">-->
    <!--      <div v-if="recentlyData.lesson_id" class="go-on-learn">-->
    <!--        <div class="go-on-learn-describe">-->
    <!--          <img-->
    <!--              class="go-on-learn-img"-->
    <!--              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/5022173314276286241077_%E6%92%AD%E6%94%BE.png"-->
    <!--          />-->
    <!--          <div class="go-on-learn-name"> {{ recentlyData.lesson_name }}</div>-->
    <!--        </div>-->
    <!--        <div class="go-on-learn-btn" @click="goLookCourse(recentlyData.lesson_id)"><span>继续学习</span></div>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <ModuleStudyVideoCourseList ref="CourseList" v-model:goods_id="goods_id" v-model:goods_pid="goods_pid" v-model:order_id="order_id"-->
    <!--                                @update="update"/>-->
  </view>
</template>
<script>
import { study } from '@/modules/jintiku/api/index.js'

export default {
  name: 'ModuleStudyVideoBaijiayunPlayback',
  data() {
    return {
      CourseList: null,
      goods_id: "",
      goods_pid: "",
      order_id: "",
      queryUrl: "",
      url: [],
      active: "1",
      options: [],
      recentlyData: {
        lesson_id: "",
        lesson_name: "",
      }
    }
  },
  props:{
	pageData:{
		type:Object
	}  
  },
  mounted() {
    this.getID();
    this.initUrl();
    this.getCourseDetailRecently();
  },
  methods: {
    getID() {
      const query = this.pageData ? this.pageData : {};
      this.goods_id = query.goods_id || "";
      this.goods_pid = query.goods_pid || "";
      this.order_id = query.order_id || "";
      this.queryUrl = query.url || "";
    },

    initUrl() {
      if (this.queryUrl) {
        if (this.queryUrl.indexOf(",") == -1) {
          this.url.push(this.queryUrl);
        } else {
          let arr = this.queryUrl.split(",");
          arr.forEach((item, index) => {
            this.url.push(item);
            this.options.push({
              text: "回放" + (index + 1),
              value: index + 1,
            });
          });
        }
      }
    },

    changeSelected(val) {
      this.active = val;
    },

    update(val) {
      this.queryUrl = val.url;
      this.url = [];
      this.options = [];
      if (this.queryUrl) {
        if (this.queryUrl.indexOf(",") == -1) {
          this.url.push(this.queryUrl);
        } else {
          let arr = this.queryUrl.split(",");
          arr.forEach((item, index) => {
            this.url.push(item);
            this.options.push({
              text: "回放" + (index + 1),
              value: index + 1,
            });
          });
        }
      }
    },

    getCourseDetailRecently() {
      study
        .courseDetailRecently({
          goods_id: this.goods_id || "",
          goods_pid: this.goods_pid || "",
          order_id: this.order_id || "",
        })
        .then(({ data }) => {
          if (data && data.lesson_id) {
            this.recentlyData = data;
          } else {
            this.recentlyData = {
              lesson_id: "",
              lesson_name: "",
            };
          }
        });
    },

    goLookCourse(lesson_id) {
      study
        .liveUrl({
          lesson_id: lesson_id,
        })
        .then(({ data }) => {
          if (data && data.playback_url) {
            this.update({ url: data.playback_url });
            this.$emit("changeLesson_id", lesson_id);
          } else {
            this.$xh.push('jintiku', `pages/study/live/index?lesson_id=${lesson_id}&url=${data.live_url}`);
          }
        })
        .catch(() => {
          uni.showToast({
            title: "获取直播地址失败！",
            icon: 'none'
          });
        });
    }
  }
}
</script>
<style lang="less" scoped>
.my-detail {
  //position: fixed;
  //top: 0;
  //left: 0;
  //width: 100vw;
  //height: 100vh;
  //overflow-y: scroll;
  min-height: 100vh;
  height: 100vh;
  width: 100vw;
  background-color: #f2f5f7;

  .video-box {
    height: 100vh;
    width: 100vw;
  }
  .baijiayun-playback-webview {
    height: 100%;
    width: 100%;
    //height: 100%;
  }

  .go-on-learn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    margin-top: 20px;
    padding: 12px 16px;
    border-radius: 6px;

    .go-on-learn-describe {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .go-on-learn-img {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }
    }

    .go-on-learn-btn {
      box-sizing: border-box;
      font-weight: 400;
      font-size: 12px;
      color: #018CFF;
      text-align: center;
      height: 28px;
      width: 72px;
      background: rgba(1, 163, 99, 0.07);
      border-radius: 14px;
      border: 1px solid rgba(1, 163, 99, 0.47);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .tabs {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    box-sizing: border-box;
    padding: 0px 20px;
    padding-top: 10px;
    overflow: hidden;

    .tabs-item {
      min-width: 50px;
      margin-right: 32px;
      font-weight: 500;
      font-size: 14px;
      color: rgba(51, 51, 51, 0.85);
    }

    .active {
      position: relative;
      font-weight: 600;
      font-size: 17px;
      color: #333333;
    }

    .active::before {
      position: absolute;
      left: calc(50% - 11px);
      bottom: -8px;
      content: "";
      width: 22px;
      height: 3px;
      background: #018CFF;
      border-radius: 1px;
    }
  }
}
</style>
