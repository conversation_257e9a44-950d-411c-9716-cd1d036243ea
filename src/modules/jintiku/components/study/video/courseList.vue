<template>
  <view class="course-list">
    <view class="course-list-content">
      <view v-if="learnCourseList.length" class="learn-course-list">
        <view v-for="(item, index) in learnCourseList" :key="index" class="learn-course-list-item">
          <view class="learn-course-list-title">
            <view class="title">
              <view class="title-name">
                <text class="teaching-type">{{ item.teaching_type_name }}</text>
                <text>{{ item.name }}</text>
                <text class="label"
                  >（{{ item.lesson_attendance_num }}/{{
                    item.lesson_num
                  }}）</text
                >
              </view>
              <view class="close-open">
                <image
                  v-if="!item.isClose"
                  class="close"
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/31c3173314287462623784_%E4%B8%8A.png"
                  @click="item.isClose = true"
                />
                <image
                  v-else
                  class="open"
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/57dd17331428838889503_%E4%B8%8B.png"
                  @click="item.isClose = false"
                />
              </view>
            </view>
            <view class="address" v-if="item.address">
              <image
                class="address-img"
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/53a1173442499072733755_dizhi.png"
              />
              {{ item.address }}
            </view>
            <view class="class-progress">
              <text class="label">班级进度</text>
              <view class="progress">
                <u-line-progress
                  :percent="
                    getProgress(item.lesson_attendance_num, item.lesson_num)
                  "
                  :show-percent="false"
                  active-color="#018CFF"
                />
              </view>
              <text class="label"
                >{{ item.lesson_attendance_num }}/{{ item.lesson_num }}</text
              >
            </view>
          </view>
          <view v-if="!item.isClose" class="learn-course-list-content">
            <view
              v-for="(lessons, index) in item.lesson"
              :key="index"
              class="learn-course-lessons"
            >
              <view
                class="learn-course-lessons-name"
                @click="goLookCourse(lessons, item.teaching_type)"
              >
                {{ lessons.lesson_name }}
              </view>
              <view
                class="learn-course-lessons-message"
                @click="goLookCourse(lessons, item.teaching_type)"
              >
                <view class="learn-course-lessons-message-left">
                  <view
                    v-if="item.teaching_type == '1'"
                    class="learn-course-lessons-type"
                  >
                    <image
                      v-if="lessons.lesson_status == '3'"
                      class="learn-course-lessons-type-img"
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/2759173314312481144525_huifang.png"
                    />
                    <!--                  <image-->
                    <!--                      v-if="lessons.status == '3'"-->
                    <!--                      class="learn-course-lessons-type-img"-->
                    <!--                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/531817331432239781486_shengchenghuifang.png"-->
                    <!--                  />-->
                    <image
                      v-if="lessons.lesson_status != '3'"
                      class="learn-course-lessons-type-img"
                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/896f173314326200816744_zhibo.png"
                    />
                    <!--                  <image-->
                    <!--                    class="learn-course-lessons-type-img"-->
                    <!--                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/52fd173314329947918956_shipin.png-->
                    <!--"-->
                    <!--                  />-->
                    <text>{{
                      lessons.lesson_status == "3" ? "回放" : "直播"
                    }}</text>
                  </view>
                  <view v-if="item.teaching_type == '1'" class="line"></view>
                  <view class="learn-course-lessons-time">
                    {{ lessons.date }}
                  </view>
                  <view class="line"></view>
                  <view class="learn-course-lessons-teacher">
                    {{ lessons.teacher }}
                  </view>
                </view>
                <view class="learn-course-lessons-message-right">
                  <text v-if="lessons.status == '2'">已学完</text>
                  <!--                <text v-if="lessons.status=='2'" style="color: #018CFF">正在学</text>-->
                  <text v-if="lessons.status != '2'" style="color: #018CFF"
                    >未学习</text
                  >
                </view>
              </view>
              <!--              :class="{'no-border':!lessons.independent.length && (index==(item.lesson.length-1))}"-->
              <view
                v-if="
                  lessons.evaluation_type_top &&
                  lessons.evaluation_type_top.length
                "
                class="operation-box"
              >
                <view
                  v-for="(btn, btnIndex) in lessons.evaluation_type_top"
                  :key="btnIndex"
                  class="operation-box-item preview"
                  @click="goAnswer(lessons, btn)"
                >
                  <image :src="completePath(btn.icon)" class="operation-img" />
                  <text class="operation-text">{{ btn.name }}</text>
                </view>
                <view
                  v-if="lessons.resource_document && lessons.resource_document.length"
                  class="operation-box-item preview"
                  @click="goDataDownload(lessons.resource_document[0].path)"
                >
                  <image
                    class="operation-img"
                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/6473173622834392761808_ziliao.png"
                  />
                  <text class="operation-text">资料</text>
                </view>
                <!--              <view v-if="lessons.pingjia == '1'" class="operation-box-item preview">-->
                <!--                <image-->
                <!--                    class="operation-img"-->
                <!--                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/9028173310453840513498_pingce.png"-->
                <!--                ></image>-->
                <!--                <text class="operation-text">评价</text>-->
                <!--              </view>-->
              </view>
              <view
                v-if="
                  lessons.evaluation_type_bottom &&
                  lessons.evaluation_type_bottom.length
                "
                class="independent-list"
              >
                <view
                  v-for="(independent, independentIndex) in lessons.evaluation_type_bottom"
                  :key="independentIndex"
                  class="independent-item"
                >
                  <view class="independent-list-item-title">
                    <image
                      :src="completePath(independent.icon)"
                      alt=""
                      class="independent-list-item-title-img"
                    />
                    <text class="independent-list-item-title-name">{{
                      independent.name
                    }}</text>
                  </view>
                  <view
                    v-if="independent.is_evaluation == '1'"
                    class="independent-list-item-operate over"
                    @click="goAnswer(lessons, independent)"
                  >
                    <text>已完成</text>
                  </view>
                  <view
                    v-if="independent.is_evaluation == '2'"
                    class="independent-list-item-operate go-exam"
                    @click="goAnswer(lessons, independent)"
                  >
                    <text>去考试</text>
                  </view>
                  <!--                <view v-if="independent.status== '3'" class="independent-list-item-operate unlock">-->
                  <!--                  <image alt=""-->
                  <!--                       src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/795d173431941362452725_suo2.png">-->
                  <!--                  <text>解锁</text>-->
                  <!--                </view>-->
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="loading" style="text-align: center; margin-top: 20px">
      <uni-load-more status="loading"></uni-load-more>
    </view>
  </view>
</template>
<script>
import { study } from '@/modules/jintiku/api/index.js'

export default {
  name: 'ModuleStudyVideoCourseList',
  props: {
    goods_id: {
      type: String,
      default: ""
    },
    goods_pid: {
      type: String,
      default: ""
    },
    order_id: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      loading: true,
      learnCourseList: []
    }
  },
  mounted() {
    this.getCourseDetailLessons()
  },
  methods: {
    getCourseDetailLessons() {
      study
        .courseDetailLessons({
          goods_id: this.goods_id,
          goods_pid: this.goods_pid,
          order_id: this.order_id,
        })
        .then(({ data }) => {
          if (data && data.length) {
            this.learnCourseList = data;
            this.learnCourseList = this.learnCourseList
              .map((item) => {
                return {
                  ...item,
                  lesson: item.lesson && item.lesson.map ? item.lesson.map((lesson) => {
                    return {
                      ...lesson,
                      evaluation_type_top: lesson.evaluation_type.filter(
                        (btn) =>
                          btn.is_separate == "2" &&
                          btn.paper_version_id &&
                          btn.paper_version_id != "0"
                      ),
                      evaluation_type_bottom: lesson.evaluation_type.filter(
                        (btn) =>
                          btn.is_separate == "1" &&
                          btn.paper_version_id &&
                          btn.paper_version_id != "0"
                      ),
                    };
                  }) : []
                };
              })
              .filter((item) => {
                const query = this.$mp && this.$mp.query ? this.$mp.query : {};
                return item.id == query.filter_goods_id;
              });
          } else {
            this.learnCourseList = [];
          }
        })
        .catch((err) => {
          console.log(err);
          uni.showToast({
            title: err.msg && err.msg[0] ? err.msg[0] : '获取课程列表失败',
            icon: 'none'
          });
          if (err.code == 100002) {
            uni.showToast({
              title: err.msg && err.msg[0] ? err.msg[0] : '登录已过期',
              icon: 'none'
            });
            this.studyLoginLose();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    goLookCourse(lesson, teaching_type) {
      if (!teaching_type || teaching_type != "1") {
        return;
      }
      study
        .liveUrl({
          lesson_id: lesson.lesson_id,
        })
        .then(({ data }) => {
          if (data && data.playback_url) {
            this.$emit("update", {
              url: data.playback_url,
            });
            this.getCourseDetailLessons();
          } else {
            this.$xh.push('jintiku', `pages/study/live/index?lesson_id=${lesson.lesson_id || ""}&url=${data.live_url}`);
          }
        })
        .catch(() => {
          uni.showToast({
            title: "获取直播地址失败！",
            icon: 'none'
          });
        });
    },

    goDataDownload(url) {
      if (!url) {
        uni.showToast({
          title: "暂无资料",
          icon: 'none'
        });
        return;
      }
      this.preview(this.completePath(url));
    },

    goAnswer(lessons, btn) {
      this.$xh.push('jintiku',
        `pages/answertest/answer/index?paper_version_id=${btn.paper_version_id || ""}&evaluation_type_id=${btn.id || ""}&professional_id=${btn.professional_id || ""}&goods_id=${lessons.paper_goods_id || ""}&order_id=${lessons.order_id || ""}&system_id=${lessons.system_id || ""}&order_detail_id=${lessons.order_goods_detail_id || ""}&lesson_id=${lessons.lesson_id || ""}`
      );
    },

    // 拼接完整路径
    completePath(path) {
      // TODO: 需要配置OSS基础URL
      return 'https://ysys-assets.oss-cn-beijing.aliyuncs.com' + path;
    },

    getProgress(overNum, totalNum) {
      if (!totalNum || !overNum || totalNum == "0" || overNum == "0") {
        return 0;
      }
      return Math.round(Number((overNum / totalNum) * 100));
    },

    preview(url) {
      // 预览文件
      this.$xh.push('jintiku', `pages/webview/index?url=${encodeURIComponent(url)}`);
    },

    studyLoginLose() {
      // 处理登录失效逻辑
      console.log('登录失效');
    }
  }
}
</script>
<style lang="less" scoped>
.course-list {
  padding: 0px 12px;

  .course-list-content {
    padding-bottom: 100px;

    .learn-course-list {
      margin-top: 11px;

      .learn-course-list-item {
        background: #fff;
        border-radius: 6px;
        overflow: hidden;

        .learn-course-list-title {
          background: linear-gradient(
            180deg,
            #e8fff2 0%,
            #f1fff8 50%,
            #f4fffb 100%
          );
          padding: 18px;

          .title {
            font-weight: 500;
            font-size: 15px;
            color: #262629;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 14px;

            .title-name {
              display: flex;
              justify-content: flex-start;
              align-items: center;

              .teaching-type {
                width: 31px;
                height: 17px;
                background: #018CFF;
                border-radius: 2px;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                font-weight: 400;
                font-size: 11px;
                color: #ffffff;
                margin-right: 6px;
              }
            }

            .close-open {
              display: flex;
              justify-content: flex-start;
              align-items: center;

              .close {
                width: 17px;
                height: 17px;
              }

              .open {
                width: 17px;
                height: 17px;
              }
            }
          }

          .address {
            .address-img {
              width: 14px;
              height: 14px;
              margin-right: 5px;
            }

            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            font-weight: 400;
            line-height: 18px;
            font-size: 12px;
            color: #424b57;
            margin-bottom: 16px;
          }

          .class-progress {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .label {
              font-weight: 400;
              font-size: 12px;
              color: #424b57;
            }

            .progress {
              margin: 0px 17px 0px 9px;

              flex: 1;
            }
          }
        }

        .learn-course-list-content {
          padding: 18px 16px 0px;

          .learn-course-lessons {
            //padding-bottom: 11px;
            margin-bottom: 19px;
            border-bottom: 1px solid rgba(232, 233, 234, 0.6);

            .learn-course-lessons-name {
              margin-bottom: 11px;
              font-weight: 600;
              font-size: 15px;
              color: #262629;
              line-height: 22px;
            }

            .learn-course-lessons-message {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 20px;
              font-weight: 400;
              font-size: 12px;
              color: #93969f;

              .learn-course-lessons-message-left {
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .learn-course-lessons-type {
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                  font-weight: 400;
                  font-size: 12px;
                  color: #424b57;

                  .learn-course-lessons-type-img {
                    width: 12px;
                    height: 12px;
                    margin-right: 7px;
                  }
                }

                .line {
                  width: 1px;
                  height: 10px;
                  background: #e1e5e8;
                  margin: 0 6px;
                }

                .learn-course-lessons-time {
                }

                .learn-course-lessons-teacher {
                }
              }

              .learn-course-lessons-message-right {
              }
            }

            .operation-box {
              display: flex;
              justify-content: flex-end;
              align-items: center;
              font-weight: 400;
              font-size: 12px;
              color: #424b57;
              padding-bottom: 22px;
              border-bottom: 1px solid #e8e9ea;

              .operation-box-item {
                display: flex;
                justify-content: flex-start;
                align-items: center;
              }

              .operation-box-item:not(:last-child) {
                position: relative;
                padding-right: 24px;
                margin-right: 24px;
              }

              .operation-box-item:not(:last-child)::after {
                content: "";
                width: 1px;
                height: 10px;
                background: #eceff1;
                position: absolute;
                right: 0;
                top: 2px;
              }

              .operation-img {
                width: 14px;
                height: 14px;
                margin-right: 3px;
              }

              .operation-text {
                color: #424b57;
              }
            }

            .no-border {
              //border-bottom: none;
            }

            .independent-list {
              padding-top: 10px;
              padding-bottom: 10px;
              .independent-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 0;
                //border-bottom: 1px solid #E8E9EA;

                .independent-list-item-title {
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;

                  .independent-list-item-title-img {
                    width: 24px;
                    height: 24px;
                    marign-right: 8px;
                  }

                  .independent-list-item-title-name {
                    font-weight: 600;
                    font-size: 14px;
                    color: #262629;
                  }
                }

                .independent-list-item-operate {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  font-weight: 400;
                  font-size: 12px;
                  color: #93969f;
                  margin-right: 18px;
                }

                .over {
                }

                .go-exam {
                  margin-right: 0px;
                  font-weight: 400;
                  font-size: 12px;
                  color: #018CFF;
                  width: 72px;
                  height: 28px;
                  border-radius: 14px;
                  border: 1px solid rgba(1, 163, 99, 0.56);
                  text-align: center;
                  line-height: 26px;
                }

                .over {
                  margin-right: 0px;
                  font-weight: 400;
                  font-size: 12px;
                  width: 72px;
                  height: 28px;
                  border-radius: 14px;
                  border: 1px solid #e2e2e2;
                  text-align: center;
                  line-height: 26px;
                }

                .unlock {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin-right: 0px;
                  font-weight: 400;
                  font-size: 12px;
                  color: #969696;
                  width: 72px;
                  height: 28px;
                  border-radius: 14px;
                  border: 1px solid transparent;
                  background: #efefef;

                  img {
                    width: 14px;
                    height: 14px;
                    margin-right: 2px;
                  }
                }
              }
            }
          }
        }

        .learn-course-lessons:last-child {
          margin-bottom: 0;
          border-bottom: none;

          .independent-list {
            .independent-item:last-child {
              border-bottom: 0px;
            }
          }
        }
      }

      .learn-course-list-item:not(:first-child) {
        margin-top: 12px;
      }
    }
  }
}
</style>
