<template>
  <view class="data-download">
    <u-search
        v-model="value"
        placeholder="搜索文档名称"
        @search="search"
        @input="search"
    />
    <view class="file-list">
      <view v-for="(item, index) in copyList" :key="index" class="file-item">
        <view class="file-icon">
          <image :src="item.src" alt=""></image>
        </view>
        <view class="file-message">
          <view class="file-name">{{ item.name }}</view>
          <view class="file-size-page">
            <view class="file-info">
              <view class="file-size">{{ item.num }}下载</view>
              <view class="file-page">{{ item.Pages }}页</view>
            </view>
            <view class="file-operate">
              <!--              <view class="file-operate-item file-download" @click="download(item)">下载</view>-->
              <view class="file-operate-item file-preview" @click="preview(item)">预览</view>
              <!--                            <view class="file-operate-item file-not" @click="preview(item)">-->
              <!--                              <image src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4c2517335433227937920_jinzhi.png" alt="">-->
              <!--                              <text>未解锁</text>-->
              <!--                            </view>-->
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="loading" style="text-align: center;margin-top: 50px;">
      <uni-load-more status="loading"></uni-load-more>
    </view>
  </view>
</template>
<script>
import { study } from '@/modules/jintiku/api/index.js'

export default {
  name: 'ModuleStudyDataDownload',
  data() {
    return {
      value: '',
      loading: true,
      list: [],
      copyList: []
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      study
        .dataDownload({
          lessons_id: ''
        })
        .then(({data}) => {
          this.list = data || []
          this.copyList = this.list
        })
        .finally(() => {
          this.loading = false
        })
    },

    search(item) {
      let key = typeof item === 'string' ? item : (item.data || item)
      this.copyList = this.list.filter((item) => {
        if (!key) {
          return true
        }
        return item.name.includes(key)
      })
    },

    download(item) {
      // 在uni-app中使用uni.downloadFile或者uni.openDocument
      uni.downloadFile({
        url: item.link,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log('打开文档成功')
              }
            })
          }
        }
      })
    },

    preview(item) {
      // 预览文件
      this.$xh.push('jintiku', `pages/webview/index?url=${encodeURIComponent(item.link)}`)
    }
  }
}
</script>
<style lang="less" scoped>
.data-download {
  .u-search {
    padding: 12px 16px 0px;
  }

  .file-list {
    padding: 23px 16px;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #E8E9EA;

      .file-icon {
        img {
          width: 35px;
          height: 40px;
          margin-right: 17px;
        }
      }

      .file-message {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-content: space-between;

        .file-name {
          font-weight: 500;
          font-size: 15px;
          color: #262629;
        }

        .file-size-page {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .file-info {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            font-weight: 400;
            font-size: 12px;
            color: rgba(38, 38, 41, 0.6);

            .file-size {
              margin-right: 10px;
            }

            .file-page {

            }
          }

          .file-operate {
            .file-operate-item {
              width: 72px;
              height: 26px;
              border-radius: 14px;
              font-weight: 400;
              font-size: 12px;
              text-align: center;
              line-height: 24px;
            }

            .file-download {
              color: #018CFF;
              border: 1px solid rgba(1, 163, 99, 0.47);
            }

            .file-preview {
              background: #EAFFF7;
              border: 1px solid #0EA96D;
              color: #0EA96D;
            }

            .file-not {
              display: flex;
              justify-content: center;
              align-items: center;
              background: #EFEFEF;
              color: #969696;

              img {
                width: 14px;
                height: 14px;
                margin-right: 2px;
              }
            }
          }

        }


      }
    }
  }
}
</style>
