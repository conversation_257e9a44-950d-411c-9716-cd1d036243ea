<template>
	<view class="study-card-grid">
		<u-grid :border="false" :col="2" @click="handleGridClick">
			<!-- 历年真题 -->
			<u-grid-item name="0" :customStyle="cardStyle">
				<view class="study-card">
					<view class="card-icon">
						<img class="icon-bell" src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/col-1.png" alt="" />
					</view>
					<view class="card-content">
						<text class="card-title">历年真题</text>
						<text class="card-subtitle">冲刺必刷 快速提分</text>
					</view>
				</view>
			</u-grid-item>

			<!-- 高频考点 -->
			<u-grid-item name="1" :customStyle="cardStyle">
				<view class="study-card">
					<view class="card-icon">
						<img class="icon-bell" src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/col-2.png" alt="" />
					</view>
					<view class="card-content">
						<text class="card-title">科目模考</text>
						<text class="card-subtitle">阶段测试</text>
					</view>
				</view>
			</u-grid-item>

			<!-- 易混错题 -->
			<u-grid-item name="2" :customStyle="cardStyle">
				<view class="study-card">
					<view class="card-icon">
						<img class="icon-bell" src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/col-3.png" alt="" />
					</view>
					<view class="card-content">
						<text class="card-title">模拟考试</text>
						<text class="card-subtitle">重难点总结</text>
					</view>
				</view>
			</u-grid-item>

			<!-- 难点突破 -->
			<u-grid-item name="3" :customStyle="cardStyle">
				<view class="study-card">
					<view class="card-icon">
						<img class="icon-bell" src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/col-4.png" alt="" />
					</view>
					<view class="card-content">
						<text class="card-title">学习报告</text>
						<text class="card-subtitle">理论强化</text>
					</view>
				</view>
			</u-grid-item>
		</u-grid>
	</view>
</template>

<script>
	export default {
		name: 'StudyCardGrid',
		data() {
			return {
				// 自定义卡片样式
				cardStyle: {
					padding: '6rpx',
					height: '160rpx'
				},
				navs: ['pages/questionChallenge/index', 'pages/intelligentEvaluation/index', 'pages/examEntry/index',
					'pages/modelExaminationCompetition/index',
				],
			}
		},
		methods: {
			// 处理 u-grid 的点击事件
			handleGridClick(name) {
				this.handleCardClick(name)
			},

			handleCardClick(type) {
				this.$emit('cardClick', type)
				
				let {
					major_id = '', major_name = ''
				} =
				uni.getStorageSync('__xingyun_major__')

				// 根据不同类型跳转到不同页面
				switch (type) {
					case '0':
						this.$xh.push(
							'jintiku',
							`pages/test/detail?professional_id=${major_id}&id=578830359412613856`)
						break
					case '1':
						this.$xh.push(
							'jintiku',
							`pages/test/detail?professional_id=${major_id}&id=578830434356437728`)
						break
					case '2':
						this.$xh.push(
							'jintiku',
							`pages/test/detail?professional_id=${major_id}&id=578821217692164832`)
						break
					case '3':
						this.$xh.push('jintiku', 'pages/userInfo/report')
						break
					default:
						console.log('Unknown card type:', type)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.study-card-grid {
		padding: 10rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		background: transparent;

		.study-card {
			background-color: white;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			padding: 32rpx;
			border-radius: 32rpx;
			// box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
			overflow: hidden;
			margin-bottom: 0rpx;

			&:active {
				transform: scale(0.98);
				transition: transform 0.1s ease;
			}

			.card-icon {
				width: 60rpx;
				height: 60rpx;
				margin-right: 25rpx;
				flex-shrink: 0;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 100%;
					height: 100%;
				}

				// Emoji图标样式
				.icon-diamond,
				.icon-bell,
				.icon-tooth,
				.icon-edit {
					font-size: 40rpx;
					line-height: 1;
				}
			}

			.card-content {
				flex: 1;

				.card-title {
					display: block;
					font-size: 28rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 6rpx;
					line-height: 1.5;
				}

				.card-subtitle {
					display: block;
					font-size: 22rpx;
					color: #666;
					line-height: 1.5;
				}
			}
		}
	}

	// 响应式适配
	@media screen and (max-width: 750rpx) {
		.study-card-grid {
			.study-card {
				.card-title {
					font-size: 26rpx;
				}

				.card-subtitle {
					font-size: 20rpx;
				}

				.card-icon {
					width: 50rpx;
					height: 50rpx;

					.icon-diamond,
					.icon-bell,
					.icon-tooth,
					.icon-edit {
						font-size: 36rpx;
					}
				}
			}
		}
	}
</style>