<template>
  <div class="qrcode-box">
    <view class="tips">扫码加牙开心刷题小助手，获取最新备考资料</view>
    <view class="qrcode">
      <image
        src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/WechatIMG357.jpg"
        show-menu-by-longpress
      ></image>
    </view>
  </div>
</template>

<script>
export default {
  props: {
    professional_id_name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      qrcode: '',
      // 护士，药师调中医的
      // 乡村掉临床的
      qrcodeList: [
        {
          key: ['口腔'],
          src: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/60dc174341121431254320_b597430dda7e46cc7fe564a6aa6416a.png'
        },
        {
          key: ['临床', '乡村'],
          src: 'https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/WechatIMG357.jpg'
        },
        {
          key: ['中医', '护士', '药师', '西医', '中西医'],
          src: 'https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/WechatIMG357.jpg'
        }
      ],
      allQrcode:
        'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/2ec4174341105901736218_tongyongma.png'
    }
  },
  created() {
    //按照专业展示
    let major_name = uni.getStorageSync('__xingyun_major__').major_name || ''
    if (this.professional_id_name) {
      major_name = this.professional_id_name
    }
    this.qrcode = this.allQrcode
    this.qrcodeList.forEach(item => {
      item.key.forEach(key => {
        let reg = new RegExp(key)
        if (reg.test(major_name)) {
          this.qrcode = item.src
        }
      })
    })
  }
}
</script>

<style lang="scss" scoped>
.qrcode-box {
  // width: 686rpx;
  // height: 726rpx;
  background: #ffffff;
  border-radius: 24rpx;
  margin: 0 24rpx 0 24rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 80rpx;
  .tips {
    padding-top: 40rpx;
    text-align: center;
    font-weight: 400;
    font-size: 28rpx;
    color: rgba(32, 32, 32, 0.85);
    line-height: 40rpx;
  }
  .qrcode {
    margin-top: 68rpx;
    display: flex;
    width: 310rpx;
    height: 310rpx;
    align-items: center;
    justify-content: center;
    background-size: 310rpx 310rpx;
    background-image: url(https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/46c2174185637060211201_%E5%BD%A2%E7%8A%B6%E7%BB%93%E5%90%88%402x.png);
    image {
      width: 238rpx;
      height: 238rpx;
      background: #e4e8ed;
    }
  }
}
</style>
