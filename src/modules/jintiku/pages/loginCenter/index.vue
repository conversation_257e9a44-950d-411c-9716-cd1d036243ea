<template>
  <view>
    <!--  #ifdef MP-WEIXIN  -->
    <view class="loding">
      <view class="logo">
        <image
          src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/yakaixindf.png"
          alt=""
        />
      </view>
      <view class="name">牙开心</view>
      <login @success="handleSuccess">
        <view class="login_button button">手机号快捷登录</view>
      </login>
    </view>
    <!--  #endif -->
    <!--  #ifdef H5  -->
    <login-h5 @success="handleSuccess"></login-h5>
    <!--  #endif -->
  </view>
</template>

<script>
import login from '../../components/commen/login.vue'
import loginH5 from '../../components/commen/login-h5.vue'
export default {
  components: {
    login,
    loginH5
  },
  methods: {
    handleSuccess() {
      // uni.navigateBack({
      //   delta: 1
      // })
      // 获取当前页面栈
      let pages = getCurrentPages()
      // 当前页面是页面栈的最后一个，上一页是倒数第二个
      let prevPage = pages[pages.length - 2]
      // 获取上一页的页面路径
      let prevPagePath = prevPage.route
      // uni.redirectTo('/modules/jintiku/pages/index/index')
      // modules/jintiku/pages/index/index
      console.log(prevPagePath, '99')
      if (prevPagePath == 'modules/jintiku/pages/examination/examinationing') {
        // uni.redirectTo({
        //   url: '/modules/jintiku/pages/index/index',
        //   fail(err) {
        //     console.log(err)
        //   }
        // })

        uni.navigateBack({
          delta: 1
        })
      } else if (prevPagePath == 'modules/jintiku/pages/zhuanti/skillBlackRoom') {
        console.log(99999)
        this.$xh.push('jintiku', 'pages/zhuanti/skillBlackRoom')
      } else {
        // uni.switchTab({
        //   url: '/modules/jintiku/pages/index/index',
        //   fail(err) {
        //     console.log(err)
        //   }
        // })
        uni.navigateBack({
          delta: 1
        })
      }
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style lang="less" scoped>
.loding {
  .logo {
    width: 120rpx;
    height: 120rpx;
    margin: 360rpx auto 56rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .name {
    font-size: 48rpx;
    font-weight: 600;
    color: #181818;
    text-align: center;
    width: 100%;
  }
  .login_button {
    width: 670rpx;
    height: 92rpx;
    border-radius: 46rpx;
    background: #2e68ff;
    color: #ffffff;
    text-align: center;
    line-height: 92rpx;
    margin: 300rpx auto;
  }
}
</style>
