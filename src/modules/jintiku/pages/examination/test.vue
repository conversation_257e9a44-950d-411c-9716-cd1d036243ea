<template>
  <view class="page-baidu">
    <!-- <view class="head-height"> -->
    <head-height
      :statusBarHeight.sync="statusBarHeight"
      style="background-color: #fff; position: relative; z-index: 11"
    />
    <!-- </view> -->
    <!-- 顶部功能预览 -->
    <view class="priview-time" :style="{ top: statusBarHeight + 'px' }">
      <view
        class="top-head"
        :style="{ height: statusBarHeight + 20 + 'px' }"
        style="background-color: #fff"
      ></view>
      <view class="button success" @click="showModelfn"> 交卷 </view>
      <view class="nums">
        <text>{{ current + 1 }}</text>
        <text>/{{ lists.length }}</text>
      </view>
    </view>
    <view style="height: auto; overflow: hidden">
      <view class="h96"></view>
      <!-- 第几轮 -->
      <view class="title">
        <text>{{ title }}</text>
      </view>
      <view class="time">
        剩余考试时间：{{ getResidueTime(transformTime(time)) }}
      </view>
      <view class="uni-margin-wrap">
        <examination-question-swiper
          :lists.sync="lists"
          :index.sync="current"
          ref="exercise"
          @last="last"
          :examination_id="id"
          :examKey="examKey"
          v-if="lists.length"
        />
      </view>
      <view class="utils">
		  <view
		    class="gjb dtk button"
		    @click="shareFuntion()"
		    style="width: 78rpx"
		  >
		    <image
		      src="/static/imgs/jintiku/buy-selected.png"
		      mode="widthFix"
		    />
		    <text>问老师</text>
		  </view>
        <view
          class="gjb dtk button"
          @click="sheetShow = true"
          style="width: 78rpx"
        >
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16950896298723a451695089629872551_dtk.png"
            mode="widthFix"
          />
          <text>答题卡</text>
        </view>

        <view class="gjb dtk button" @click="onDoubtClick" style="width: 78rpx">
          <image
            :src="
              lists[current].doubt
                ? 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16956222038717b4a169562220387182065_%E7%BC%96%E7%BB%84%205%E5%A4%87%E4%BB%BD%402x.png'
                : 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967375279005243169673752790025047_%E5%85%A8%E9%83%A8%E8%A7%A3%E6%9E%90%E5%A4%87%E4%BB%BD%205%402x.png'
            "
            mode="widthFix"
          />
          <text :class="{ doubt: lists[current].doubt }">
            {{ lists[current].doubt ? '已标疑' : '标疑' }}
          </text>
        </view>
        <view class="gjb pre">
          <view class="btn button flex-center" @click="prev">上一题</view>
        </view>
        <view class="gjb next">
          <view class="btn button flex-center" @click="next">下一题</view>
        </view>
      </view>
    </view>

    <model
      v-model="showModel"
      :title="title"
      :desc="desc"
      :sure="sure"
      :cancel="cancel"
      @success="success"
    />
    <!-- 答题卡 -->
    <answer-sheet
      v-model="sheetShow"
      @change="sheetchange"
      :questions="lists"
    />
    <test-reminder-overtime
      :modelValue="overtimeModelValue"
      @close="overtimeModelValue = false"
      :addTimeInfo="addTimeInfo"
      v-if="overtimeModelValue"
    />
    <test-reminder-suspend
      :modelValue="suspendModelValue"
      @close="suspendModelValue = false"
      v-if="suspendModelValue"
    />
    <test-reminder-enforcement
      :modelValue="enforcementModelValue"
      @close="enforcementModelValue = false"
      @submit="handInPapers"
      v-if="enforcementModelValue"
    />
    <test-reminder-abnormal
      :modelValue="abnormalModelValue"
      @close="abnormalModelValue = false"
      v-if="abnormalModelValue"
    />
  </view>
</template>
<script>
import md5 from 'md5'
import { transformTime } from '../../utils/index.js'
import questionAnswer from '../../components/makeQuestion/question-answer.vue'
import answerSheet from '../../components/makeQuestion/answer-sheet.vue'
import model from '../../components/commen/uni-model.vue'
import mixin from '../../mixin/index.js'
import examinationQuestionSwiper from '../../components/makeQuestion/examination-question-swiper-new.vue'
import headHeight from '../../components/commen/head-height.vue'
import { setQuestionLists } from '../../utils/index.js'
// 几个弹框
import testReminderOvertime from '../../components/makeQuestion/tipDialog/test-reminder-overtime.vue'
import testReminderSuspend from '../../components/makeQuestion/tipDialog/test-reminder-suspend'
import testReminderEnforcement from '../../components/makeQuestion/tipDialog/test-reminder-enforcement'
import testReminderAbnormal from '../../components/makeQuestion/tipDialog/test-reminder-abnormal'
import {
  getQuestionsDetail,
  doSecond,
  // getjinTiKuMockExamLists,
  submitAnswer,
  getquestionlistchap,
  examSubmitAnswer,
  getTimeOut,
  getLock,
  getPause,
  // getMessage,
  getIsSubmit,
  setLastId
} from '../../api/index.js'
export default {
  mixins: [mixin],
  components: {
    questionAnswer,
    model,
    answerSheet,
    examinationQuestionSwiper,
    headHeight,
    testReminderOvertime,
    testReminderEnforcement,
    testReminderSuspend,
    testReminderAbnormal
  },
  data() {
    return {
      overtimeModelValue: false,
      suspendModelValue: false,
      enforcementModelValue: false,
      abnormalModelValue: false,
      statusBarHeight: 0,
      lists: [],
      allLists: [], // 全部
      errorStatic: false,
      total: 0,
      indicatorDots: false,
      autoplay: false,
      // 当前选定值
      current: 0,
      // 当前时间
      time: 0,
      transformTime,
      // 显示答题卡
      sheetShow: false,
      // 是否交卷
      isHadnPaper: false,
      // 显示授权登录按钮
      // authorizationShow: false,
      examKey: '',
      id: '',
      // type=>6 我的错题, type => 29  每日一练交卷,  type => 1 章节练习
      // type: 1,
      // mokao: false
      eid: '',
      status: '',
      pid: '',
      session: '',
      session_name: '',
      // doubt: []
      timing: '',
      mock_name: '',
      addTimeInfo: {},
      pauseInfo: {},
      type: 7, // 不同场景
      isSubmit: false, // 是否交卷了
      switching: false,
      idIndex: null,
      //

      title: '',
      query: {}
    }
  },
  methods: {
	  shareFuntion(){
		  const f = `${wx.env.USER_DATA_PATH}/share${Math.floor(
		    Math.random() * 1000
		  )}.png`
		  wx.showShareImageMenu({
		                    path: f,
		                    success: () => {}
		                  })
	  },
    getExamKey(obj) {
      return md5(
        `${obj.student_id}${obj.professional_id}${obj.paper_version_id}`
      )
    },
    back() {
      uni.navigateBack({
        delta: 1
      })
    },
    setTime() {
      let that = this
      if (this.isSubmit) {
        return
      }
      if (this.time > 0) {
        this.timing = setTimeout(() => {
          this.time--
          this.setTime()
        }, 1000)
      } else {
        // this.handInPapers()
        // 直接交卷
        // this.$showModel(
        //   '温馨提示',
        //   `您的考试时间已到，请交卷`,
        //   '确认',
        //   '退出',
        //   () => {
        //     this.handInPapers()
        //   }
        // )
        uni.showModal({
          title: '温馨提示',
          content: '您的考试时间已到，请交卷',
          cancelText: '退出考试',
          confirmText: '确认交卷',
          success: function (res) {
            if (res.confirm) {
              // console.log('用户点击确定');
              that.handInPapers()
            } else if (res.cancel) {
              // console.log('用户点击取消');
              uni.navigateBack({
                delta: 1
              })
            }
          }
        })
      }
    },
    prev() {
      if (this.switching) {
        this.$xh.Toast('不要点太快哦！')
        return
      }
      this.switching = true
      this.$refs.exercise.prev && this.$refs.exercise.prev()
      setTimeout(() => {
        this.switching = false
      }, 300)
    },
    next() {
      if (this.switching) {
        this.$xh.Toast('不要点太快哦！')
        return
      }
      this.switching = true
      this.$refs.exercise.next && this.$refs.exercise.next()
      setTimeout(() => {
        this.switching = false
      }, 300)
    },
    last() {
      let none = this.lists.reduce((cur, next) => {
        if (next.user_option == '') {
          return cur + 1
        }
        return cur
      }, 0)
      if (!none) {
        // 直接交卷
        this.$showModel(
          '确认交卷？',
          `已经是最后一道题了，是否确认交卷?`,
          '确认',
          '再看看',
          () => {
            this.handInPapers()
          }
        )
      } else {
        this.$showModel(
          '确认交卷？',
          `还有${none}道题未作答，确定要交卷吗?`,
          '确认',
          '继续做题',
          () => {}
        )
      }
    },
    // 交卷
    handInPapers() {
      if (this.isSubmit) {
        return
      }
      // 交卷
      let obj = {}
      let tmpData = JSON.parse(JSON.stringify(this.lists))
      tmpData.forEach(item => {
        if (!obj[item.question_id]) {
          obj[item.question_id] = item
          return
        }
        obj[item.question_id].stem_list.push(item.stem_list[0])
      })
      let arr = []
      for (let key in obj) {
        arr.push(obj[key])
      }
      let cost_time = parseInt(this.examTime - this.time)
      if (cost_time < 1) {
        cost_time = 1
      }
      let type = this.type
      let question_info = arr.map(item => {
        return {
          question_id: item.question_id,
          user_option: item.stem_list.map(res => {
            return {
              sub_question_id: res.id,
              answer: res.selected.map(jtem => {
                return item.type == '8' || item.type == '9' || item.type == '10'
                  ? jtem.replace('\n', '<br/>')
                  : String(jtem)
              })
            }
          })
        }
      })

      examSubmitAnswer({
        product_id: this.query.paper_version_id,
        professional_id: this.query.professional_id,
        cost_time,
        type,
        question_info: JSON.stringify(question_info),
        goods_id: this.query.goods_id,
        order_id: this.query.order_id
        // sub_order_id: this.query.sub_order_id
      }).then(data => {
        this.isSubmit = true
        uni.redirectTo({
          url:
            `/modules/jintiku/` +
            `pages/test/examScoreReporting?professional_id=${this.query.professional_id}&paper_version_id=${this.query.paper_version_id}&order_id=${this.query.order_id}&goods_id=${this.query.goods_id}&title=${this.query.title}`
        })
      })
      let dataList = uni.getStorageSync('__anwers_list__')
      delete dataList[this.examKey]
      uni.setStorageSync('__anwers_list__', dataList)
    },
    showModelfn() {
      // 计算还有多少题没有答
      let none = this.lists.reduce((cur, next) => {
        if (next.user_option == '') {
          return cur + 1
        }
        return cur
      }, 0)
      if (!none) {
        // 直接交卷
        this.handInPapers()
      } else {
        this.$showModel(
          '确认交卷？',
          `还有${none}道题未作答，确定要交卷吗?`,
          '确认',
          '继续做题',
          () => {}
        )
      }
    },
    success() {
      this.$hideModel()
      this.handInPapers()
    },
    sheetchange(index) {
      this.current = index
    },
    getList() {
      let data = {
        paper_version_id: this.query.paper_version_id,
        type: this.type || '8'
      }
      this.setTime()
      let anwersList = uni.getStorageSync('__anwers_list__') || {}
      if (anwersList[this.examKey]) {
        this.lists = anwersList[this.examKey].lists
      } else {
        getquestionlistchap(data).then(res => {
          this.lists = setQuestionLists(res.data.section_info)
        })
      }
    },
    // 标疑
    onDoubtClick() {
      let isDoubt = this.lists[this.current].doubt
      this.lists[this.current].doubt = !this.lists[this.current].doubt
    },
    getResidueTime(time) {
      if (time.includes('NaN')) {
        uni.showModal({
          title: '考试时间有误',
          content: '请您退出考试重新进入，或者联系相关老师协助！',
          success: function (res) {}
        })
        return '00:00:00'
      }
      return time
    }
  },
  onLoad(e) {
    clearTimeout(this.timing)

    this.title = e.title
    this.time = e.time * 1
    this.examTime = e.time
    this.type = e.type ? e.type : 8
    this.query = e
    //
    this.examKey = this.getExamKey({
      paper_version_id: e.paper_version_id,
      professional_id: e.professional_id,
      student_id: uni.getStorageSync('__xingyun_userinfo__').student_id
    })

    this.getList()
  },
  onUnload() {
    clearTimeout(this.timing)
    // this.handInPapers()
  },
  onShow() {},
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.page-baidu {
  .priview-time {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 96rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: center;
    padding: 0 40rpx;
    z-index: 10;
    .top-head {
      position: absolute;
      width: 100%;
      top: -116rpx;
      left: 0;
    }
    .nums {
      text {
        font-size: 32rpx;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 32rpx;
        color: #000000;
      }
    }
    .back {
      position: absolute;
      width: 19rpx;
      height: 32rpx;
      left: 30rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
    }
    .success {
      position: absolute;
      left: 40rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
      width: 120rpx;
      height: 44rpx;
      text-align: center;
      line-height: 44rpx;
      background: #2e68ff;
      border-radius: 8rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #fff;
    }
  }
  .h96 {
    height: 96rpx;
    background-color: #fff;
    position: relative;
    z-index: 9;
  }
  .title {
    display: flex;
    height: 94rpx;
    // line-height: 94rpx;
    text-align: center;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    text {
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #000000;
    }
  }
  .time {
    height: 64rpx;
    line-height: 64rpx;
    background: #f5f8ff;
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #2e68ff;
    text-align: center;
  }
  .uni-margin-wrap {
    width: calc(100vw);
    height: calc(100vh - 330rpx);
    overflow-y: scroll;
    padding-bottom: calc(80rpx + 42rpx + 36rpx);
    // padding-top: 96rpx;
    box-sizing: border-box;
  }
  .utils {
    position: fixed;
    left: 20rpx;
	right: 0rpx;
    bottom: 0;
    // width: 100%;
    height: 80rpx;
    padding-bottom: 42rpx;
    padding-top: 36rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 158rpx;
    .gjb {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      image {
        width: 34rpx;
        height: 40rpx;
        margin-bottom: 14rpx;
      }
      text {
        font-size: 24rpx;
        color: rgba(41, 65, 90, 0.75);
      }
      .btn {
        width: 192rpx;
        height: 80rpx;
        border-radius: 40rpx;
        border: 1px solid #2e68ff;
        color: #2e68ff;
        font-size: 26rpx;
      }
      .doubt {
        color: #fb9e0c;
      }
    }
    .next {
      .btn {
        border-radius: 40rpx;
        border: 1px solid #2e68ff;
        color: #fff;
        // background: linear-gradient(270deg, #6d7dff 0%, #b3a1ff 100%);
        background: #2e68ff;
      }
    }
  }
}
</style>
