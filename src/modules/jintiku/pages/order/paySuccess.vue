<template>
  <view class="page">
    <div class="qrcode-box">
      <view class="success">
        <view class="img">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/26e2174185623886722986_%E7%BC%96%E7%BB%84%2013%402x.png"
          >
          </image>
        </view>
        <text>支付成功</text>
      </view>

      <view class="tips">便于给您及时提供服务，长按二维码加群学习</view>
      <view class="qrcode">
        <image
          src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/WechatIMG357.jpg"
          show-menu-by-longpress
        ></image>
      </view>
    </div>

    <view class="but-group">
      <view @click="home">返回</view>
      <view @click="goDetail()">开始测验</view>
    </view>
  </view>
</template>

<script>
import { getGoodsDetail } from '../../api/index'
import { setSubscribeMessage } from '../../utils/index'

export default {
  data() {
    return {
      qrcode: '',
      goods_id: '',
      info: {},
      // 护士，药师调中医的
      // 乡村掉临床的
      qrcodeList: [
        {
          key: ['口腔'],
          src: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/60dc174341121431254320_b597430dda7e46cc7fe564a6aa6416a.png'
        },
        {
          key: ['临床', '乡村'],
          src: 'https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/WechatIMG357.jpg'
        },
        {
          key: ['中医', '护士', '药师', '西医', '中西医'],
          src: 'https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/WechatIMG357.jpg'
        }
      ],
      allQrcode:
        'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/2ec4174341105901736218_tongyongma.png'
    }
  },
  onLoad(e) {
    this.goods_id = e.goods_id
    this.getGoodsDetail()
    //按照专业展示
    let major_name = uni.getStorageSync('__xingyun_major__').major_name || ''
    if (e.professional_id_name) {
      major_name = e.professional_id_name
    }
    this.qrcode = this.allQrcode
    this.qrcodeList.forEach(item => {
      item.key.forEach(key => {
        let reg = new RegExp(key)
        if (reg.test(major_name)) {
          this.qrcode = item.src
        }
      })
    })
  },
  methods: {
    home() {
      uni.navigateBack({
        delta: 1
      })
    },
    getGoodsDetail() {
      getGoodsDetail({
        goods_id: this.goods_id
      }).then(res => {
        this.info = res.data
      })
    },
    goDetail() {
      let item = this.info
      if (item.type == 18) {
        setSubscribeMessage('goods')
        this.$xh.push(
          'jintiku',
          `pages/chapterExercise/index?professional_id=${item.professional_id}&goods_id=${item.id}&total=${item.tiku_goods_details.question_num}`
        )
      }
      if (item.type == 10) {
        let url = `pages/modelExaminationCompetition/examInfo?product_id=${item.id}&title=${item.name}&page=home&professional_id=${item.professional_id}`
        if (!this.$xh.isInitName(url)) {
          setSubscribeMessage('goods')
          this.$xh.push('jintiku', url)
        }
      }
      if (item.type == 8) {
        setSubscribeMessage('goods')
        this.$xh.push('jintiku', `pages/test/exam?id=${item.id}`)
      }
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style lang="scss" scoped>
.qrcode-box {
  width: 686rpx;
  height: 726rpx;
  background: #ffffff;
  border-radius: 24rpx;
  margin: 0 32rpx 0 32rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  .tips {
    padding-top: 228rpx;
    text-align: center;
    font-weight: 400;
    font-size: 28rpx;
    color: rgba(32, 32, 32, 0.85);
    line-height: 40rpx;
  }
  .qrcode {
    margin-top: 68rpx;
    display: flex;
    width: 310rpx;
    height: 310rpx;
    align-items: center;
    justify-content: center;
    background-size: 310rpx 310rpx;
    background-image: url(https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/46c2174185637060211201_%E5%BD%A2%E7%8A%B6%E7%BB%93%E5%90%88%402x.png);
    image {
      width: 238rpx;
      height: 238rpx;
      background: #e4e8ed;
    }
  }
}
.page {
  background: #f7f7f7;
  //height: 100vh;
  padding-top: 102rpx;
  width: 100vw;
}

.success {
  position: absolute;
  z-index: 1;
  top: -62rpx;
  left: calc(50% - 103rpx);
  // margin-top: -62rpx;
  .img {
    padding: 36rpx;
    // width: 206rpx;
    // height: 206rpx;
    background-color: #fff;
    border-radius: 50%;
    image {
      width: 128rpx;
      height: 128rpx;
    }
  }

  text {
    margin-top: 10rpx;
    font-weight: 600;
    font-size: 44rpx;
    color: #121212;
  }
}
.but-group {
  padding-top: 80rpx;
  display: flex;
  justify-content: center;
  view {
    width: 264rpx;
    text-align: center;
    height: 44px;
    line-height: 44px;
    // background: #2e68ff;
    border-radius: 22px;
    font-size: 14px;
  }
  view:nth-child(1) {
    border: 1px solid #d8dde1;
    color: rgba(3, 32, 61, 0.65);
    margin-right: 12px;
  }
  view:nth-child(2) {
    color: #ffffff;
    background: #2e68ff;
  }
}
</style>
