<template>
  <view class="all-course">
    <!-- <view class="header-course">
      <view class="header-text">课程测评</view>
    </view> -->
    <view class="course-list" v-if="listData.length > 0">
      <view
        class="course-content"
        v-for="(item, index) in listData"
        :key="index"
        @click="toAnswerDetails(item)"
      >
        <view class="course-title">
          <view class="title">{{ item.goods_name }}</view>
        </view>
        <view class="subject" v-if="item.project_id_name">
          学科：{{ item.project_id_name }}
        </view>
        <view class="subject">
          <text v-if="item.goods_pname">套餐：{{ item.goods_pname }} </text>
        </view>
        <view class="segmentation"></view>
        <view>
          <view class="bottom-box">
            <view class="left-tag">
              <image
                style="width: 18px; height: 18px;"
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/b606173598990778745189_%E7%BC%96%E7%BB%84%204%402x.png"
              />
            </view>
            <view class="left-title">课时检测</view>
            <view class="question-number">
              <!-- <view
                class="noProgress"
                v-if="
                  item.do_paper_num == item.paper_num && item.paper_num == 0
                "
              >
                暂无检测
              </view>
              <view
                class="noProgress"
                v-else-if="item.do_paper_num == item.paper_num"
              >
                已完成
              </view> -->
              <view class="flex">
                <view
                  class="achieve-number"
                  :class="{
                    noProgress: item.do_paper_num == 0,
                  }"
                >
                  {{ item.do_paper_num }}
                </view>
                <view>/</view>
                <view class="all-number">
                  {{ item.paper_num }}
                </view>
              </view>
              <view class="right-img">
                <image
                  style="width: 10px; height: 10px;"
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/228e173599006726114935_%E7%BC%96%E7%BB%84%2036%402x.png"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view
      style="text-align: center; margin-top: 80px"
      v-if="listData.length == 0 || !listData"
    >
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4045173295663081752515_8b3592c2dcddcac66af8ddd46abbbf1b74efa19fac63-AlBs3V_fw1200%402x.png"
        style="width: 155px; height: 100px;"
      />
      <view class="empty-message">暂无数据</view>
    </view>
  </view>
</template>
<script>
import { answer } from '@/modules/jintiku/api/index.js'

export default {
  name: "AnswerTest",
  components: {},
  data() {
    return {
      already_do_paper_num: 11,
      paper_total: 12,
      listData: [],
      isLogin: true,
      majorid: "",
      citymajorName: "暂无",
      majorshow: false,
      majorTitle: "",
    }
  },
  onLoad(options) {
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: '课程测评'
    });

    // 处理传入的参数
    if (options.from) {
      // 处理 from 参数
    }
    if (options.phone) {
      // 处理 phone 参数
    }
    this.isLogin_status()
  },
  methods: {
    isLogin_status() {
      // 简化登录状态检查，直接调用搜索
      this.search()
    },
    handoff() {
      this.isLogin = false
    },
    getMajorInfo() {},
    goDetection(item) {
      if (item.do_paper_num == 0 && item.paper_num == 0) {
        return
      }
    },
    toAnswerDetails(val) {
      if (val.do_paper_num == 0 && val.paper_num == 0) {
        uni.showToast({
          title: "暂无检测",
          icon: 'none'
        })
        return
      }
      uni.navigateTo({
        url: `/modules/jintiku/pages/answertest/answerDetails?goods_id=${val?.goods_id}&order_detail_id=${val?.order_detail_id}&order_id=${val?.order_id}`
      })
    },
    search() {
      console.log("查询列表")

      answer
        .getMyCourseevaluation({ noParams: true })
        .then((res) => {
          if (res?.code == 100000) {
            this.listData = res.data.courses
          } else {
            uni.showToast({
              title: "请求失败，请联系管理员",
              icon: 'none'
            })
          }
        })
        .catch((err) => {
          uni.showToast({
            title: err.msg[0],
            icon: 'none'
          })
          if (err.code == 100002) {
            uni.showToast({
              title: err.msg[0],
              icon: 'none'
            })
            // 处理登录失效
            console.log('登录失效')
          }
        })
    },
  },
  // watch: {
  //   // 监听路由对象
  //   $route: {
  //     handler(to, from) {
  //       this.search()
  //     },
  //     deep: true,
  //     immediate: true,
  //   },
  // },
}
</script>

<style lang="less" scoped>
.all-course {
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  padding-bottom: 16px;
  padding-top: 18px;
  background: #f2f5f7;
  font-family: PingFangSC, PingFang SC;
  .header-course {
    padding: 8px 12px;
    border-bottom: 1px solid #f2f5f7;
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    text-align: center;
    .header-text {
      font-size: 16px;
      color: #262629;
      font-weight: 500;
    }
    .uni-calendar__header-text {
      align-items: center;
    }
    .class-name {
      color: rgba(3, 32, 61, 0.75);
      font-size: 13px;
      width: 250px;
      text-align: right;
    }
    .class-bottom-img {
      width: 9px;
      height: 9px;
      margin-left: 8px;
    }
  }
  .course-list {
    width: 100%;
    padding: 12px;
    .course-content {
      width: 100%;
      // background: url("https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/%E7%BC%96%E7%BB%84%207%402x%20(1).png");
      background-size: 100%;
      background: #ffffff;
      margin-bottom: 10px;
      border-radius: 6px;
      padding: 16px;
      padding-bottom: 14px;
      .course-title {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 12px;
        .left-tag {
          padding: 3px 8px;
          font-size: 11px;
          color: #ffffff;
          background: #2e68ff;
          border-radius: 3px;
          margin-right: 8px;
        }
        .title {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 15px;
          color: #262629;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }
      .subject {
        font-size: 12px;
        color: #424b57;
        margin-top: 10px;
      }
      .segmentation {
        width: 100%;
        height: 1px;
        background: rgba(232, 233, 234, 0.5);
        margin: 12px 0;
        margin-top: 20px;
        margin-bottom: 14px;
      }
      .bottom-box {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .left-tag {
          display: flex;
          align-items: center;
        }
        .left-title {
          font-size: 11px;
          color: #424b57;
          margin-left: 4px;
          flex-shrink: 0;
          transform: translateY(-1px);
        }
        .question-number {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          margin-left: auto;
          font-size: 13px;
          width: 100%;
          color: #424b57;
          .achieve-number {
            color: #424b57;
          }
          .noProgress {
            color: #424b57;
          }
          .all-number {
            color: rgba(66, 75, 87, 0.6);
          }
          .right-img {
            margin-left: 4px;

            image {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}
.empty-message {
  font-weight: 400;
  font-size: 12px;
  color: rgba(3, 32, 61, 0.45);
  margin-bottom: 32px;
}
</style>
