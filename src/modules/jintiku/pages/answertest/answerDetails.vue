<template>
  <div class="class-detection">
    <div class="scrollBox">
      <div class="scroll-Y">
        <div class="courseCss">{{ details?.goods_name }}</div>
        <div class="static-block">
          <div class="content flex">
            <div class="left-cicle">
              <div v-if="is_canvas">
                <ModuleAnswerCircularProgress :num="percent" />
              </div>
            </div>
          </div>
          <div class="border"></div>
          <div class="bottom flex remainingPaper">
            <div>
              <van-image
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1bb8173630503854751388_%E7%BC%96%E7%BB%84%204%E5%A4%87%E4%BB%BD%202%403x.png"
                width="11px"
                height="12px"
                class="tub"
              />
              <div class="tip">剩余试卷：</div>
            </div>
            <div class="desc">
              <text class="active">
                {{ Number(details.paper_num) - Number(details.do_paper_num) }}份
              </text>
              <text>待完成</text>
            </div>
          </div>
        </div>
        <div class="control flex">
          <div class="sift" @click="handleIsFinish">
            <text>测评分类</text>
            <span class="cornermark"></span>
          </div>
        </div>
        <div class="lists" v-if="currentList || currentList.length > 0">
          <div
            class="list"
            v-for="(item, index) in currentList"
            :key="index"
            v-show="item.is_finish != 1 || !is_finish"
            @click="toAnswer(item)"
          >
            <div class="status">
              <div class="name flex">
                <div class="round"></div>
                <div class="text">{{ item.evaluation_type_name }}</div>
              </div>
              <!-- <div class="status-tag" :class="{ noFinish: item.is_finish == 1 }"> -->
              <div
                class="status-tag"
                :class="[
                  item.is_lock == 1
                    ? 'noFinish'
                    : item.last_practice_id == 0 || !item.last_practice_id
                    ? 'dFinish'
                    : 'finish',
                ]"
              >
                {{
                  item.is_lock == 1
                    ? "未解锁"
                    : item.last_practice_id == "0" || !item.last_practice_id
                    ? "待完成"
                    : "已完成"
                }}
              </div>
            </div>
            <div class="type flex">
              <div class="title">{{ item.paper_name }}</div>
              <div class="desc"></div>
            </div>
            <!-- <div class="kejieBox">
            <div class="kejie">{{ item?.lesson_name }}</div>
          </div> -->

            <!-- <div class="gaopBox">
            <div class="gaop">{{ item?.paper_sub_type_name }}</div>
          </div> -->
            <span class="gaoptext">{{ item?.paper_sub_type_name }}</span>
            <div class="lesson flex">
              <div class="major hide-text">
                完成进度：<span style="font-weight: 500; font-size: 12px"
                  >{{ item.correct_num }}/{{ item.question_num }}</span
                >
              </div>
              <div
                class="major flex"
                style="
                  flex-shrink: 0;
                  margin-left: 3px;
                  font-size: 12px;
                  color: #424b57;
                "
              >
                正确率：<span class="accuracy">{{ item.correct_rate }}</span>
                <!-- <div class="right-img">
                <image
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/%E7%BC%96%E7%BB%84%204%E5%A4%87%E4%BB%BD%203%402x.png"
                />
              </div> -->
              </div>
            </div>
          </div>
        </div>
        <div
          style="text-align: center; margin-top: 80px"
          v-if="!currentList || currentList.length == 0"
        >
          <van-image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4045173295663081752515_8b3592c2dcddcac66af8ddd46abbbf1b74efa19fac63-AlBs3V_fw1200%402x.png"
            width="155px"
            height="100px"
          />
          <div class="empty-message">暂无数据</div>
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="showSearch"
      position="top"
      round=""
      :style="{ height: '50%' }"
    >
      <div class="searchBox">
        <text class="title">测评分类</text>
        <span class="cornermarkBox"></span>
      </div>
      <div class="select-content">
        <div class="select-content-left">
          <div class="select-menu">
            <div
              v-for="(tag, i) in searchOne"
              :key="i"
              :class="{ active: leftActiveId == tag.id }"
              @click.stop="getLeftInfo(tag)"
            >
              {{ tag.name }}
            </div>
          </div>
        </div>
        <div class="select-content-right">
          <div class="select-menu">
            <div
              v-for="(tag, i) in searchTwo"
              :key="i"
              @click.stop="getRightInfo(tag)"
              :class="{ active: rightActiveId == tag.id }"
            >
              <div class="nameBox">
                {{ tag.name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
const { answer } = useApi()

export default {
  name: "all-course",
  components: {},
  data() {
    return {
      major_name: "",
      lessons: [],
      id: "",
      currentList: [],
      info: {
        all_num: "100", // 总数
        finish_num: "20", // 完成数
        finish_rate: "10", // 完成率
        list: [],
        tag_list: [],
        order_id: "",
        sub_order_id: "",
      },
      is_finish: false,
      showSearch: false,
      searchOne: [
        // {
        //   name: "全部",
        //   id: "all",
        // },
        // {
        //   name: "课前",
        //   id: "class_before",
        // },
        // {
        //   name: "课中",
        //   id: "class_during",
        // },
        // {
        //   name: "课后",
        //   id: "class_after",
        // },
      ],
      leftActiveId: "",
      searchTwo: [
        // {
        //   name: "周",
        //   id: 1,
        // },
        // {
        //   name: "月",
        //   id: 2,
        // },
        // {
        //   name: "学科",
        //   id: 3,
        // },
        // {
        //   name: "月度考试",
        //   id: 4,
        // },
      ],
      rightActiveId: "",
      typeTreeCode: "",
      goods_id: "",
      order_detail_id: "",
      order_id: "",
      evaluation_type_id: "",
      details: {},
      percent: 0,
      is_canvas: false,
    }
  },
  mounted() {
    useHead({
      title: " ",
    })
    // const route = useRoute()
    // // route.query
    // // goods_id   order_detail_id    order_id
    // this.goods_id = route.query?.goods_id
    // this.order_detail_id = route.query?.order_detail_id
    // this.order_id = route.query?.order_id
    // this.searchDetailsData()
    // this.getSearchData()
  },
  methods: {
    getMock() {
      return {
        all_num: "100", // 总数
        finish_num: "20", // 完成数
        finish_rate: "10", // 完成率
        list: [],
        tag_list: [
          {
            all_num: "20", // 总数
            finish_num: "10", // 完成数
            id: "ididid",
            name: "A类型", // 标签名称
          },
          {
            all_num: "20", // 总数
            finish_num: "10", // 完成数
            id: "ididid",
            name: "B类型", // 标签名称
          },
        ],
        is_finsih: false,
      }
    },
    searchDetailsData() {
      let data = {
        goods_id: this.goods_id,
        order_detail_id: this.order_detail_id,
        order_id: this.order_id,
      }
      answer
        .getCourseevaluationDetail({ ...data })
        .then((res) => {
          this.$nextTick(() => {
            this.details = res?.data
            if (Number(this.details?.correct_rate) > 0) {
              this.percent = res?.data?.correct_rate / 100
            } else {
              this.percent = 0
            }
            this.is_canvas = true
            console.log("this.percent", this.percent)
          })
        })
        .catch((err) => {
          showFailToast(err.msg[0])
          if (err.code == 100002) {
            showFailToast(err.msg[0])
            answertestLoginLose()
          }
        })
    },
    getSearchData() {
      let data = {
        goods_id: this.goods_id,
        order_detail_id: this.order_detail_id,
        order_id: this.order_id,
        evaluation_type_id: this.evaluation_type_id,
      }

      answer
        .getCourseevaluationList({ ...data })
        .then((res) => {
          if (res?.data?.course_evaluations?.length > 0) {
            this.currentList = res?.data?.course_evaluations
          } else {
            this.currentList = []
          }
        })
        .catch((err) => {
          showFailToast(err.msg[0])
          if (err.code == 100002) {
            showFailToast(err.msg[0])
            answertestLoginLose()
          }
        })
    },
    toAnswer(val) {
      console.log("valvalval", val)
      if (val?.is_lock == 1) {
        return
      }
      const router = useRouter()
      router.push(
        `/answertest/answer?paper_version_id=${val?.paper_version_id}&professional_id=${val?.professional_id}&goods_id=${val?.goods_id}&order_id=${val?.order_id}&evaluation_type_id=${val?.evaluation_type_id}&system_id=${val?.system_id}&order_detail_id=${val?.order_detail_id}&lesson_id=${val?.lesson_id}`
      )
    },
    handleIsFinish() {
      this.getAllTypeTree("class_before,class_during,class_after")
      this.showSearch = true
    },
    getLeftInfo(item) {
      console.log("item", item)
      this.leftActiveId = item.id
      if (item?.subs?.length > 0) {
        this.searchTwo = item?.subs
        this.rightActiveId = item?.subs[0]?.id
        this.evaluation_type_id = item?.subs[0]?.id
      } else {
        this.searchTwo = []
        this.evaluation_type_id = ""
      }
      this.getSearchData()
    },
    getRightInfo(item) {
      console.log("item", item)
      this.rightActiveId = item?.id
      this.evaluation_type_id = item?.id
      this.getSearchData()
    },
    // 获取全部分类
    getAllTypeTree(code) {
      console.log("code", code)
      answer
        .getEvaluationTypeTree({
          code,
        })
        .then((res) => {
          console.log("获取测评分类", res)

          this.searchOne = [
            {
              name: "全部",
              id: "all",
            },
            ...res?.data,
          ]

          if (this.searchOne?.length > 0) {
            this.leftActiveId = "all"
            this.searchTwo = []
            this.evaluation_type_id = ""
            this.getSearchData()
            // let arr = []
            // if (res?.data?.length > 0) {
            //   res?.data?.forEach((item) => {
            //     arr = [...arr, ...item?.subs]
            //   })
            //   this.searchTwo = arr
            // }
          }

          // if (res?.data?.length > 0) {
          //   this.leftActiveId = res?.data[0]?.id
          //   if (res?.data[0]?.subs?.length) {
          //     this.searchTwo = res?.data[0]?.subs
          //     this.rightActiveId = res?.data[0]?.subs[0]?.id
          //     this.evaluation_type_id = res?.data[0]?.subs[0]?.id
          //     this.getSearchData()
          //   }
          // }
        })
        .catch((err) => {
          showFailToast(err.msg[0])
          if (err.code == 100002) {
            showFailToast(err.msg[0])
            answertestLoginLose()
          }
        })
    },
  },
  watch: {
    // 监听路由对象
    $route: {
      handler(to, from) {
        const route = useRoute()
        // route.query
        // goods_id   order_detail_id    order_id
        this.goods_id = route.query?.goods_id
        this.order_detail_id = route.query?.order_detail_id
        this.order_id = route.query?.order_id
        this.searchDetailsData()
        this.getSearchData()
      },
      deep: true,
      immediate: true,
    },
  },
  // watch: {
  //   showSearch: {
  //     handler(val) {
  //       console.log("弹框", val)
  //       if (!val) {
  //         this.leftActiveId = "all"
  //         this.getAllTypeTree("class_before,class_during,class_after")
  //       }
  //     },
  //     immediate: true,
  //   },
  // },
}
</script>

<style lang="scss" scoped>
.class-detection {
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  background: #f2f5f7;
  border-top: 1px solid #f2f5f7;
  .scrollBox {
    background-image: url("https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/88fb173493785239149172_%E7%BC%96%E7%BB%84%2011%402x.png");
    background-repeat: no-repeat;
    background-position: 0px -75px;
    background-size: contain;
    background-attachment: scroll;
  }
  .scroll-Y {
    // height: 100vh;
    // padding: 12px;
    padding: 0 12px;
    width: 100%;
    box-sizing: border-box;
    .courseCss {
      width: 250px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #262629;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      padding: 31px 0;
    }
    .static-block {
      padding: 18px 16px;
      padding-bottom: 15px;
      background: linear-gradient(180deg, #ffffff 0%, #ffffff 15%);
      border-radius: 6px;
      margin-bottom: 16px;
      width: 100%;
      margin: 0 auto;
      .title {
        margin-bottom: 8px;
        position: relative;
        align-items: center;
        .tag {
          height: 18px;
          background: #2e68ff;
          border-radius: 3px;
          text-align: center;
          padding: 0 8px;
          margin-right: 8px;
          font-weight: 400;
          font-size: 11px;
          color: #ffffff;
          line-height: 18px;
        }
        .tag-title {
          width: 250px;
          font-weight: 600;
          font-size: 14px;
          color: #262629;
          line-height: 22px;
          text-align: left;
        }
        .select {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          margin: auto 0;
          width: 16px;
          height: 16px;
        }
      }
      .content {
        .left-cicle {
          // display: flex;
          // align-items: center;
          // justify-content: flex-start;
          margin: 0 auto;
        }
        .ul {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .li {
            text-align: right;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #5b6e81;
            line-height: 22px;
            margin-bottom: 3px;
            .block {
              width: 3px;
              height: 3px;
              background: #2e68ff;
              margin-right: 9px;
            }
          }
        }
      }
      .border {
        height: 1px;
        width: 100%;
        border-bottom: 1px dashed rgba(232, 233, 234, 0.7);
        margin-top: 19px;
        margin-bottom: 16px;
      }
      .bottom {
        justify-content: space-between;
        .tub {
          transform: translateY(1px);
        }
        .tip {
          font-weight: 400;
          font-size: 13px;
          color: #93969f;
          line-height: 18px;
          display: inline-block;
          margin-left: 8px;
        }
        .desc {
          font-weight: 400;
          font-size: 13px;
          color: #93969f;
          line-height: 18px;
          text-align: center;
          .active {
            color: #12aa6f;
          }
        }
      }
    }
    .control {
      margin-top: 16px;
      margin-bottom: 16px;
      align-items: center;
      .sift {
        display: flex;
        align-items: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 15px;
        color: #262629;
        text-align: left;
        font-style: normal;
        .select {
          width: 12px;
          height: 12px;
        }
        .unselected {
          width: 12px;
          height: 12px;
        }
        text {
          margin-left: 8px;
        }
      }
      .cornermark {
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 0px solid transparent;
        border-bottom: 6px solid #0fa86c;
        transform: translate(3px, 3px);
      }
    }
    .lists {
      padding-bottom: 25px;
      .list {
        margin: 0 auto;
        margin-bottom: 10px;
        // width: 356px;
        background: #ffffff;
        border-radius: 6px;
        overflow: hidden;
        .status {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          .status-tag {
            width: 55px;
            height: 22px;
            background: #ffefdc;
            border-radius: 7px 0px 8px 0px;
            // background: url("https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1714103031908b66b171410303190984739_daizuoda.png")
            //   no-repeat;
            // background-size: cover;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #ff6100;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            padding: 3px 9px;
          }
          .noFinish {
            width: 55px;
            height: 22px;
            background: #e7e7e7;
            border-radius: 0px 7px 0px 8px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #8b9198;
            line-height: 16px;
            text-align: center;
            font-style: normal;
            padding: 3px 9px;
          }
          .dFinish {
            width: 55px;
            height: 22px;
            background: #dffcf0;
            border-radius: 0px 7px 0px 8px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #01a363;
            line-height: 16px;
            text-align: center;
            font-style: normal;
            padding: 3px 9px;
          }
          .finish {
            width: 55px;
            height: 22px;
            background: #ffefdc;
            border-radius: 0px 7px 0px 8px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #ff6100;
            line-height: 16px;
            text-align: center;
            font-style: normal;
            padding: 3px 9px;
          }
          .status-tag {
          }
          .name {
            height: 27px;
            align-items: center;
            padding: 18px 16px 0px 16px;
            .round {
              width: 5px;
              height: 5px;
              background: #11aa6d;
              border-radius: 50%;
              margin-right: 6px;
            }
            .text {
              font-weight: 400;
              font-size: 12px;
              color: #262629;
              line-height: 17px;
            }
          }
          .text {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 13px !important;
            color: #424b57 !important;
            line-height: 15px;
            text-align: left;
            font-style: normal;
          }
        }
        .type {
          justify-content: space-between;
          padding: 0 16px;
          margin: 10px 0;
          margin-top: 12px;
          margin-bottom: 2px;
          .title {
            font-weight: 600;
            font-size: 15px;
            color: #262629;
            line-height: 22px;
          }
          .desc {
            font-weight: 400;
            font-size: 13px;
            color: #5b6e81;
            line-height: 22px;
          }
        }
        .kejieBox {
          padding-left: 16px;
          margin-bottom: 12px;
          width: 100%;
          height: 17px;
          .kejie {
            height: 17px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #424b57;
            line-height: 17px;
            text-align: left;
            font-style: normal;
          }
        }
        .gaoptext {
          display: inline-block;
          padding-left: 16px;
          margin-bottom: 12px;
          margin-top: 6px;
          margin-left: 16px;
          // width: 58px;
          height: 18px;
          background: #eaeaea;
          border-radius: 2px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 11px;
          color: #424b57;
          line-height: 11px;
          // text-align: center;
          font-style: normal;
          padding: 4px 6px;
        }
        .gaopBox {
          padding-left: 16px;
          margin-bottom: 12px;
          margin-top: 6px;
          // width: 100%;
          height: 18px;
          .gaop {
            width: 58px;
            height: 18px;
            background: #eaeaea;
            border-radius: 2px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 11px;
            color: #424b57;
            line-height: 11px;
            // text-align: center;
            font-style: normal;
            padding: 4px 6px;
          }
        }
        .lesson {
          align-items: center;
          // width: 319px;
          // height: 50px;
          margin: 0 auto;
          justify-content: flex-start;
          font-weight: 400;
          font-size: 13px;
          color: #424b57;
          line-height: 18px;
          border-top: 1px solid rgba(232, 233, 234, 0.5);
          padding: 15px 16px;
          .accuracy {
            color: #11aa6d;
            font-weight: 500;
            font-size: 13px;
          }
        }
      }
    }
  }
  .right-img {
    width: 16px;
    height: 16px;
    margin-left: 4px;
    margin-top: 2px;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
.select-content {
  height: 100%;
  display: flex;
  .select-content-left {
    width: 110px;
    background: #f2f5f7;
    overflow-y: auto;
    padding-bottom: 15px;
    .select-menu {
      height: 54px;
      background: #f2f5f7;
      width: 100%;
      text-align: center;
      line-height: 48px;
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #03203d;
      .active {
        z-index: 9999;
        // width: 110px;
        // transform: translateX(-6px);
        background-color: #f9fafb !important;
      }
    }
  }
  .select-content-right {
    flex: 1;
    background: #ffffff;
    overflow-y: auto;
    padding-bottom: 15px;
    // padding-left: 23px;
    .select-menu {
      height: 54px;
      background: #ffffff;
      width: 100%;
      text-align: left;
      line-height: 48px;
      font-size: 13px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #03203d;
      .nameBox {
        padding-left: 23px;
      }
      .active {
        z-index: 9999;
        background-color: #f9fafb !important;
      }
    }
  }
}
.hide-text {
  margin-right: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
  color: #424b57;
}
.searchBox {
  height: 54px;
  width: 100%;
  background-color: #f2f5f7;
  padding-left: 16px;
  padding-top: 16px;
  .title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 15px;
    color: #262629;
    line-height: 21px;
    text-align: left;
    font-style: normal;
  }
}
.empty-message {
  font-weight: 400;
  font-size: 12px;
  color: rgba(3, 32, 61, 0.45);
  margin-bottom: 32px;
}
.remainingPaper {
}
</style>
