<template>
  <clientOnly>
    <div class="score_reporting">
      <div class="header-box">
        <div class="header">
          <!-- <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169509193147644ed169509193147782088_back.png"
            mode="widthFix"
            class="back"
            @click="back"
          /> -->
          <div class="major">
            <text>成绩报告</text>
          </div>
        </div>
      </div>
      <div class="data_list">
        <div class="evaluate list_box">
          <div class="title_text">你的综合实力</div>
          <div class="evaluate_text">
            <text v-if="data.session_score.is_pass == 1"> 及格 </text>
            <text v-if="data.session_score.is_pass == 2"> 不及格 </text>
          </div>
          <div class="session">{{ data.session_score.examination_round }}</div>
          <div class="grade">
            <div class="grade_number">{{ data.session_score.score }}</div>
            <!-- <div class="grade_text">分数</div> -->
          </div>
        </div>
        <div class="list_box">
          <div class="title">成绩明细</div>
          <div class="content_list">
            <div>
              <div class="name">客观题得分</div>
              <div class="number">
                {{ getScore(data) }}
              </div>
            </div>
            <!-- <div>
              <div class="name">主观题得分</div>
              <div class="number">
                {{ `${data.session_score.subjective_item_score}分` }}
              </div>
            </div> -->
            <div>
              <div class="name">试卷满分</div>
              <div class="number">
                {{ data.session_score.full_mark_score || "-" }}分
              </div>
            </div>
            <div>
              <div class="name">及格分</div>
              <div class="number">
                {{ data.session_score.passing_score || "-" }}分
              </div>
            </div>
          </div>
        </div>
        <div class="list_box" v-if="isShowSF">
          <div class="title">失分试题</div>
          <div>
            <table
              v-if="
                data.lose_points_question && data.lose_points_question.length
              "
            >
              <tr style="border: 0 none">
                <th style="" class="line line1">题号</th>
                <th style="" class="line2">得分</th>
                <th style="" class="line3">对应知识点</th>
              </tr>
              <div>
                <tr
                  v-for="(item, index) in data.lose_points_question"
                  class="lose_list"
                  :key="index"
                >
                  <td style="width: 60px; border-right: 0 none">
                    {{ item.sort || "-" }}
                  </td>
                  <td style="width: 60px; border-right: 0 none">
                    {{ item.get_score || "-" }}/{{ item.score || "-" }}
                  </td>
                  <td style="width: 100%">{{ item.knowledge || "-" }}</td>
                </tr>
              </div>
            </table>
            <div v-else style="display: flex; justify-content: center">
              <img
                style="width: 192px; height: 22px; margin-top: 11px"
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/datatpls/%E7%BC%96%E7%BB%84%2010%402x.png"
              />
            </div>
          </div>
        </div>
        <div class="list_box" style="overflow: hidden">
          <div class="title" style="margin-bottom: 13px">答题详情</div>
          <ModuleAnswerParticulars
            :data="data.answer_question_details"
            :isCorrect="data.session_score.is_correct"
          />
          <!-- <answer-particulars
          :data="data.answer_question_details"
          :isCorrect="data.session_score.is_correct"
        /> -->
        </div>
        <div class="not_more">没有更多啦~</div>
      </div>
      <div class="background"></div>
      <div class="empty-button" @click="reanswerBtn">重新答题</div>
    </div>
  </clientOnly>
</template>
<script>
const { answer } = useApi()
export default {
  // components: { answerParticulars },
  props: {},
  data() {
    return {
      router: useRouter(),
      data: {},
      statusBarHeight: 40,
      teaching_system_relation_id: "",
      master_order_id: "",
      goods_id: "",
      go_home: true,

      paper_version_id: "",
      professional_id: "",
      evaluation_type_id: "",
      order_id: "",
      system_id: "",
      order_detail_id: "",
      lesson_id: "",
    }
  },
  computed: {
    isShowSF() {
      if (!this.data.answer_question_details) {
        return false
      }
      let res = this.data.answer_question_details.filter((item) => {
        return !this.isSubjective(item.question_type)
      })
      return !!res.length
    },
  },
  methods: {
	  getScore(data) {
	  	let score = 0;
	  	if (data && data.session_score && data.session_score.objective_item_score) {
	  		score += data.session_score.objective_item_score ? data.session_score.objective_item_score : 0
	  	}
	  
	  	if (data && data.session_score && data.session_score.is_correct == 1) {
	  		score += data.session_score.subjective_item_score ? data.session_score.subjective_item_score : 0
	  	}
	  
	  	if (score == 0) {
	  		return '-分'
	  	} else {
	  		return score + '分'
	  	}
	  },
    init() {
      let data = {
        paper_version_id: this.paper_version_id,
        master_order_id: this.order_id,
        goods_id: this.goods_id,
        teaching_system_relation_id: this.system_id,
      }

      answer
        .getScorereporting(data)
        .then((res) => {
          this.data = res?.data
          this.data.answer_question_details =
            res?.data.answer_question_details.map((item) => {
              return {
                ...item,
                question_list: item.question_list.map((citem) => {
                  return {
                    ...citem,
                    resource_info: citem.resource_info
                      ? JSON.parse(citem.resource_info)
                      : {},
                  }
                }),
              }
            })
          console.log(this.data)
        })
        .catch((err) => {
          showFailToast(err.msg[0])
          if (err.code == 100002) {
            showFailToast(err.msg[0])
            answertestLoginLose()
          }
        })
    },
    reanswerBtn() {
      console.log("重新答题")
      const router = useRouter()
      router.push(
        `/answertest/answer?paper_version_id=${this?.paper_version_id}&professional_id=${this?.professional_id}&goods_id=${this?.goods_id}&order_id=${this?.order_id}&evaluation_type_id=${this?.evaluation_type_id}&system_id=${this?.system_id}&order_detail_id=${this?.order_detail_id}&lesson_id=${this?.lesson_id}&is_reanswer=1`
      )
    },
    isSubjective(type) {
      return type == "8" || type == "10" || type == "14" // 14案例题
    },
    // transform(data) {
    // return data
    // switch (data) {
    //   case '1':
    //     return '及格'
    //   case '2':
    //     return '不及格'
    //   default:
    //     return '-'
    // }
    // }
  },
  mounted() {
    useHead({
      title: " ",
    })
    const route = useRoute()
    console.log("route", route.query.paper_version_id)
    this.goods_id = route.query?.goods_id
    this.order_id = route.query?.order_id
    this.evaluation_type_id = route.query?.evaluation_type_id
    this.system_id = route.query?.system_id
    this.professional_id = route.query?.professional_id
    this.paper_version_id = route.query?.paper_version_id
    this.order_detail_id = route.query?.order_detail_id
    this.lesson_id = route.query?.lesson_id
    this.init()
  },
}
</script>
<style lang="scss" scoped>
.score_reporting {
  position: relative;
  .header-box {
    padding-top: 40px;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 2;
    background: #2e68ff;
    .header {
      position: relative;
      height: calc(48px);
      display: flex;
      align-items: center;
      justify-content: center;
      // margin-top: --status-bar-height;
      .back {
        position: absolute;
        width: 10px;
        height: 16px;
        left: 15px;
        top: 0;
        bottom: 0;
        margin: auto 0;
      }

      .major {
        display: flex;
        align-items: center;
        justify-content: center;
        text {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #fff;
          line-height: 18px;
        }
      }
    }
  }
  .data_list {
    margin-top: 110px;
    z-index: 1;
    position: relative;
    padding: 16px 12px;
    .list_box {
      padding: 15px 16px;
      background: #ffffff;
      border-radius: 6px;
      margin-bottom: 16px;
      .title {
        font-size: 16px;
        color: #161f30;
        font-weight: 500;
      }
      .content_list {
        width: 100%;
        padding: 10px;
        background: #f1f8ff;
        margin-top: 11px;
        // display: flex;
        // justify-content: space-between;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        // grid-template-rows: repeat(2, 1fr);
        border-radius: 4px;
        .name {
          font-size: 13px;
          color: #787e8f;
          text-align: center;
        }
        .number {
          font-size: 14px;
          color: #2e68ff;
          margin-top: 10px;
          text-align: center;
          flex-shrink: 0;
        }
        .up {
          color: #44d7b6;
        }
        .down {
          color: #e02020;
        }
      }
    }

    .evaluate {
      position: relative;
      .title_text {
        color: #787e8f;
        font-size: 11px;
        margin-bottom: 10px;
      }
      .evaluate_text {
        font-size: 18px;
        font-weight: 400;
        color: #161f30;
        margin-bottom: 10px;
        display: inline-block;
        background-image: linear-gradient(to top, #2e68ff 35%, white 35%);
      }
      .session {
        color: #161f30;
        font-size: 13px;
        font-weight: 400;
      }
      .grade {
        width: 114px;
        height: 91px;
        position: absolute;
        top: -22px;
        right: 17px;
        background: url("https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967627423535267169676274235372918_%E7%BC%96%E7%BB%84%2014%E5%A4%87%E4%BB%BD%202%402x.png");
        background-size: 114px 91px;
        .grade_number {
          font-size: 19px;
          color: #2e68ff;
          font-weight: 500;
          text-align: center;
          margin-top: 26px;
        }
        .grade_text {
          font-size: 12px;
          color: #ffffff;
          text-align: center;
          margin-top: 2px;
        }
      }
    }
  }
  table {
    width: 100%;
    font-size: 13px;
    color: #161f30;
    margin-top: 12px;
    border-left: 1px solid #d7e5fe;
    border-right: 1px solid #d7e5fe;
    position: relative;
    tr,
    td {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    tr {
      width: 100%;
      border-bottom: 1px solid #d7e5fe;
      // &:first-child {
      //   border: 0 none;
      // }
    }
    .line {
      width: 60px;
      border-top: 1px solid #d7e5fe;
      &::after {
        height: 100%;
        width: 1px;
        background-color: #d7e5fe;
        position: absolute;
        left: 43px;
        top: 0;
        content: "";
      }
    }
    .line2 {
      width: 60px;
      border-top: 1px solid #d7e5fe;
      &::after {
        height: 100%;
        width: 1px;
        background-color: #d7e5fe;
        position: absolute;
        left: 87px;
        top: 0;
        content: "";
      }
    }
    .line3 {
      width: 100%;
      border-top: 1px solid #d7e5fe;
    }

    th {
      background: #f1f8ff;
      height: 35px;
      line-height: 35px;
      text-align: center;
      border-right: 0;
    }
    td {
      text-align: center;
      line-height: 35px;
      border-top: 0;
      border-right: 0;
      min-height: 35px;
      border-right: 1px solid #d7e5fe;
    }
    td:last-child,
    th:last-child {
      border-right: 0;
    }
    td:first-child,
    th:first-child {
      border-left: 0;
    }
  }
  .background {
    background: linear-gradient(
      180deg,
      #2e68ff 0%,
      #5197fe 43%,
      rgba(255, 255, 255, 0) 100%
    );
    width: 100%;
    height: 175px;
    position: absolute;
    top: -24px;
    left: 0;
  }
  .not_more {
    line-height: 40px;
    text-align: center;
    font-size: 12px;
    color: #ccc;
    width: 100%;
    margin-bottom: 25px;
  }
  .empty-button {
    position: fixed;
    bottom: 20px;
    width: 60%;
    height: 34px;
    background: #2e68ff;
    border-radius: 22px;
    font-weight: 400;
    font-size: 13px;
    color: #ffffff;
    text-align: center;
    line-height: 34px;
    z-index: 111111;
    left: 0;
    right: 0;
    margin: auto;
  }
}
</style>
