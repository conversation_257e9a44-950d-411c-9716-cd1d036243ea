<template>
  <view class="question-index">
    <view class="backgroud-color-blue">

    </view>
    <view class="nav-bar"></view>
    <!-- 专业切换 -->
    <view class="header-box">
      <view class="header">
        <view v-if="false" class="head" @click="goDetail('pages/userInfo/index')">
          <image
            src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/yakaixindf.png"
            mode="widthFix" />
        </view>
        <view class="major flex-center" @click="selectMajorFn">
          <text>{{ major_name }}</text>
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16986561288221b15169865612882259776_select.png"
            mode="widthFix" />
        </view>
      </view>
    </view>
    <view class="banner-view">
      <u-swiper style="height: 200rpx;" :radius="'32rpx'" :list="bannerList" @click="bannerClick"></u-swiper>
    </view>
    
    <!-- 新增：学习日历组件 -->
    <study-calendar 
      :persist-days="studyStats.persistDays"
      :total-questions="studyStats.totalQuestions"
      :accuracy-rate="studyStats.accuracyRate"
      :is-checked-in="studyStats.isCheckedIn"
      @check-in="handleCheckIn"
    />
    
    <view>
      <study-card-grid @cardClick="handleCardClick" />
    </view>
    <view class="content">
      <view class="title">
        <view class="line"></view>
        <text>章节模考</text>
      </view>
      <index-nav ref="indexNav"></index-nav>
      <view class="title">
        <view class="line"></view>
        <text>温故知新</text>
      </view>
      <view class="cards">
        <view class="card" @click="goDetail('pages/wrongQuestionBook/index')">
          <view class="left flex-center">
            <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1698657134199359f169865713419968157_error.png"
              mode="widthFix" />
            <text>错题本</text>
          </view>
          <image src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1698657211781665c169865721178158161_r.png"
            mode="widthFix" />
        </view>
        <view class="card" @click="goDetail('pages/collect/index')">
          <view class="left flex-center">
            <image
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1698657147417974b169865714741794597_coll.png"
              mode="widthFix" />
            <text>试题收藏</text>
          </view>
          <image src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1698657211781665c169865721178158161_r.png"
            mode="widthFix" />
        </view>
      </view>
      <view v-if="preInfo.practice_progress_text" style="width: 100%; height: 102rpx"></view>
    </view>

    <!-- 选择专业组件 -->
    <select-major v-model="majorid" :show.sync="majorshow" :major_name.sync="major_name"
      @input="chackMajor"></select-major>
  </view>
</template>
<script>
import selectMajor from '../../components/select-major.vue'
import StudyCalendar from '../../components/study-calendar.vue'  // 正确的导入路径
import {
  getAllExam,
  getPreInfo,
  getGoods,
  activityRaram
} from '../../api/index'
import StudyCardGrid from '../../components/commen/study-card-grid.vue'
import indexNav from '../../components/commen/index-nav.vue'
import { goToLogin, goToMajor, isVersionUpdata } from '../../utils/index'
import { shelf_platform_id } from '../../config'

export default {
  components: {
    selectMajor,
    StudyCalendar,  // 注册组件
    StudyCardGrid,
    indexNav,
  },
  data() {
    return {
      bannerList: [
        "https://uviewui.com/swiper/swiper1.png",
        "https://uviewui.com/swiper/swiper2.png",
        "https://uviewui.com/swiper/swiper3.png",
      ],
      majorid: '',
      major_name: '选择专业',
      majorshow: false,
      preInfo: {
        scene: 0,
        practice_progress_text: ''
      },
      // 新增：学习统计数据
      studyStats: {
        persistDays: 31,
        totalQuestions: 700,
        accuracyRate: 12,
        isCheckedIn: true
      },
      query: {}
    }
  },
  onLoad(e) {
    console.log(e, '首页参数')
    this.query = e
    this.init(e)
    this.loadStudyStats()  // 加载学习统计数据
    
    isVersionUpdata(() => {
      if (!this.$store.state.jintiku.token) {
        this.$store.dispatch('jintiku/UNlOGIN')
        goToLogin('goToLogin33')
      }
      this.majorid = ''
      this.major_name = '选择专业'
      this.preInfo = {
        scene: 0,
        practice_progress_text: ''
      }
    })
  },
  onShow() {
    this.startFun()
    this.autoUpdate()
    this.activityRecord()
    this.refreshStudyStats()  // 刷新学习统计数据
  },
  onHide() {
    this.majorshow = false
  },
  methods: {
    bannerClick(index) {
      console.log('点击轮播图:', index)
    },
    handleCardClick(type) {
      console.log('点击了卡片:', type)
    },
    
    // 新增：处理打卡事件
    async handleCheckIn() {
      try {
        // 调用打卡API
        const result = await this.checkInAPI()
        if (result.success) {
          // 更新打卡状态
          this.studyStats.isCheckedIn = true
          this.studyStats.persistDays += 1
          
          // 保存打卡记录到本地存储
          const today = new Date().toDateString()
          uni.setStorageSync('lastCheckInDate', today)
          
          uni.showToast({
            title: '打卡成功！',
            icon: 'success',
            duration: 2000
          })
        }
      } catch (error) {
        console.error('打卡失败:', error)
        uni.showToast({
          title: '打卡失败，请重试',
          icon: 'error',
          duration: 2000
        })
      }
    },
    
    // 新增：打卡API调用（模拟）
    async checkInAPI() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true })
        }, 1000)
      })
    },
    
    // 新增：加载学习统计数据
    async loadStudyStats() {
      try {
        // 这里应该调用实际的API获取数据
        // const stats = await this.$api.getStudyStats()
        
        // 模拟数据
        const stats = {
          persistDays: 31,
          totalQuestions: 700,
          accuracyRate: 12,
          isCheckedIn: this.checkTodayCheckIn()
        }
        
        this.studyStats = stats
      } catch (error) {
        console.error('加载学习统计失败:', error)
      }
    },
    
    // 新增：刷新学习统计数据
    refreshStudyStats() {
      this.loadStudyStats()
    },
    
    // 新增：检查今日是否已打卡
    checkTodayCheckIn() {
      const today = new Date().toDateString()
      const lastCheckIn = uni.getStorageSync('lastCheckInDate')
      return lastCheckIn === today
    },
    
    // 原有方法保持不变
    init(e) {
      if (e?.scene) {
        this.xyppid = e.scene
        this.$store.commit('jintiku/setXyppid', e.scene)

        activityRaram({
          id: e.scene
        }).then(res => {
          this.$store.commit(
            'jintiku/setEmployeeId',
            res.data.xy_employee_id || ''
          )
        })
      }
      // ... 其他初始化代码
    },
    
    isLogin() {
      return !!this.$store.state.jintiku.token
    },
    
    goDetail(url) {
      if (!this.isLogin()) {
        goToLogin('goToLogin44')
        return
      }
      this.$xh.push('jintiku', url)
    },
    
    startFun() {
      let { major_id = '', major_name = '' } =
        uni.getStorageSync('__xingyun_major__')
      if (this.majorid && major_id && this.majorid != major_id) {
        this.major_name = major_name
        this.majorid = major_id
      } else if (major_id && major_name) {
        this.major_name = major_name
        this.majorid = major_id
      } else {
        if (!this.isLogin()) {
          return
        }
        if (this.query.major_id && this.query.major_name) {
          uni.setStorageSync('__xingyun_major__', {
            major_id: this.query.major_id,
            major_name: this.query.major_name
          })
        } else {
          uni.setStorageSync('__xingyun_major__', {
            major_id: '524033912737962623',
            major_name: '口腔执业医师'
          })
        }
        goToMajor()
        return
      }
      this.getPreDesc()
    },

    getPreDesc() {
      if (!this.isLogin()) {
        return
      }
      getPreInfo({
        noloading: true
      }).then(data => {
        this.preInfo = data.data
      })
    },
    
    chackMajor(value) {
      this.major_id = value
      setTimeout(() => {
        // 刷新相关数据
      }, 500)
    },
    
    selectMajorFn() {
      this.majorshow = !this.majorshow
    },
    
    // 活动记录
    activityRecord() {
      if (this.$store.state.jintiku.xyppid && this.isLogin()) {
        this.$store.dispatch('jintiku/ACTIVITY_RECORD')
      } else {
        console.log(this.xyppid, 'this.xyppid')
        if (this.xyppid) {
          this.$store.commit('jintiku/setXyppid', this.xyppid)
          this.$store.dispatch('jintiku/ACTIVITY_RECORD')
        }
      }
    },
    
    autoUpdate() {
      // 自动更新逻辑
    }
  },
  onPullDownRefresh() {
    this.refreshStudyStats()
    uni.stopPullDownRefresh()
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>

<style scoped lang="less">
// 这里可以复用原有的样式
.question-index {
  background-color: #fff;
  min-height: 100vh;
  overflow-y: auto;

  .backgroud-color-blue {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    pointer-events: none;
    background: linear-gradient(180deg,
        #B8E8FC 0%,
        #E9F7FF 100%,
        rgba(255, 255, 255, 0) 100%);
  }

  .header-box {
    padding-top: 80rpx;
    width: 100%;

    .header {
      position: relative;
      z-index: 2;
      height: calc(96rpx);
      display: flex;
      align-items: center;
      padding-left: 24rpx;

      .major {
        display: flex;
        align-items: center;
        justify-content: center;

        text {
          font-size: 32rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          line-height: 32rpx;
        }

        image {
          width: 20rpx;
          height: 20rpx;
          margin-left: 20rpx;
        }
      }
    }
  }

  .banner-view {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: 0 30rpx;
    height: 200rpx;
    box-sizing: border-box;
  }

  .content {
    background: #fff;
    position: relative;
    z-index: 1;
    border-top-right-radius: 32rpx;
    border-top-left-radius: 32rpx;
    padding: 32rpx 24rpx 60rpx 24rpx;

    .title {
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #161f30;
      line-height: 32rpx;
      margin-bottom: 24rpx;
      display: flex;
      align-items: center;

      .line {
        width: 6rpx;
        height: 30rpx;
        background: #2e68ff;
        border-radius: 4rpx;
        margin-right: 10rpx;
      }
    }

    .cards {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 42rpx;

      .card {
        width: 340rpx;
        height: 90rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #eff4ff;
        border-radius: 16rpx;
        padding: 0 24rpx;

        text {
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #000;
          line-height: 30rpx;
          margin-right: 46rpx;
        }

        image {
          width: 12rpx;
          height: 22rpx;
        }

        .left {
          image {
            width: 34rpx;
            height: 34rpx;
            margin-right: 20rpx;
          }
        }
      }
    }
  }
}
</style>
