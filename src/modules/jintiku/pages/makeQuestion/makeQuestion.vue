<template>
  <view class="page-baidu">
    <head-height :statusBarHeight.sync="statusBarHeight" />
    <!-- 顶部功能预览 -->
    <view class="priview-time" :style="{ top: statusBarHeight + 'px' }">
      <view class="back" @click="back">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169509193147644ed169509193147782088_back.png"
          mode="widthFix"
        />
      </view>
      <view class="button error" @click="lookError">
        {{ errorStatic ? '全部' : '只看错题' }}
      </view>
      <view class="nums">
        <text>{{ current + 1 }}</text>
        <text>/{{ errorStatic ? lists.length : total }}</text>
      </view>
    </view>
    <view class="uni-margin-wrap">
      <chapter-exercise-question-swiper
        v-if="lists.length && upData"
        :lists.sync="lists"
        :index.sync="current"
        ref="exercise"
        @getNextPage="getNextPage"
      ></chapter-exercise-question-swiper>
    </view>
    <!-- 底部操作按钮 -->
    <bottom-utils
      :current.sync="current"
      :lists.sync="lists"
      :isnextChapter="isnextChapter == 1"
      @nextChapter="getNextChapter"
    ></bottom-utils>

    <view v-if="show" class="share" @click="show = false">
      <view class="share-body" @click.stop="() => {}">
        <image
          class="logo"
          src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/yakaixindf.png"
        ></image>
        <view class="text">分享到微信群 解锁更多题目</view>
        <button open-type="share" class="share-but">点击分享</button>
      </view>
    </view>
  </view>
</template>
<script>
import {
  transformTime,
  setQuestionLists,
  questionHelper,
  setSubmitPageData,
  getNextChapter,
  shareAppMessage2
} from '../../utils/index.js'
import questionAnswer from '../../components/makeQuestion/question-answer.vue'
import resolveAuthorizationSheet from '../../components/makeQuestion/resolve-authorization-sheet.vue'
import mixin from '../../mixin/index.js'
import chapterExerciseQuestionSwiper from '../../components/makeQuestion/chapter-exercise-question-swiper-new.vue'
import headHeight from '../../components/commen/head-height.vue'
import { getQuestionsList, setAnswer } from '../../api/commen'
import bottomUtils from '../../components/makeQuestion/bottom-utils.vue'
import { isSelectedType } from '../../utils/index'
let questionListData = []
let allLists = []
export default {
  mixins: [mixin],
  components: {
    questionAnswer,
    resolveAuthorizationSheet,
    chapterExerciseQuestionSwiper,
    headHeight,
    bottomUtils
  },
  data() {
    return {
      show: false,
      startDate: +new Date(),
      statusBarHeight: 0,
      lists: [],
      // allLists: [], // 全部
      errorStatic: false, // 是否只看错题
      total: 0,
      // 当前选定值
      current: 0,
      // 当前时间
      time: 0,
      transformTime,
      id: '',
      knowledge_id: '',
      type: '',
      teaching_system_package_id: '',
      chapter_id: '',
      product_id: '',
      errorShow: false,
      isnextChapter: '1',
      upData: true,
      query: {}
    }
  },
  computed: {
    currentData() {
      if (!this.lists.length) {
        return {}
      }
      return this.lists[this.current]
    }
  },
  methods: {
    back() {
      uni.navigateBack({
        delta: 1
      })
    },
    selecte(info) {
      this.lists = this.lists.map(res => {
        if (res.questionid == info.questionid) {
          return {
            ...res,
            ...info
          }
        }
        return res
      })
      this.next()
    },
    // setTime() {
    //   setTimeout(() => {
    //     this.time++
    //     this.setTime()
    //   }, 1000)
    // },
    prev() {
      this.$refs.exercise.prev && this.$refs.exercise.prev()
    },
    next() {
      this.$refs.exercise.next && this.$refs.exercise.next()
    },
    lookError() {
      // 只看错题
      if (!this.errorStatic) {
        // 打开看错题状态
        let res = this.lists.find(item => {
          return (
            questionHelper.isSelected(item) && !questionHelper.diffAnswer(item)
          )
        })
        if (res) {
          this.errorStatic = true
          // 说明有做错的题目
          allLists = this.lists // 先存一下
          // 只取错题
          this.lists = this.lists.filter(item => {
            return (
              questionHelper.isSelected(item) &&
              !questionHelper.diffAnswer(item)
            )
          })
          this.current = 0
          this.upDataQuestionSwiper()
        } else {
          this.$xh.Toast('暂时还没有错题哦！')
        }
      } else {
        // 关闭错题状态
        this.errorStatic = false
        this.lists = allLists // 恢复
        this.current = 0
        this.upDataQuestionSwiper()
      }
    },
    upDataQuestionSwiper() {
      this.upData = false
      setTimeout(() => {
        this.upData = true
      }, 100)
    },
    submit() {
      let resultQuestion = setSubmitPageData(this.lists).filter(item => {
        return item.user_option.find(res => {
          return !!res.answer.length
        })
      })
      let obj = {
        type: '1',
        question_info: JSON.stringify(resultQuestion),
        cost_time: Math.floor((+new Date() - this.startDate) / 1000),
        product_id: this.product_id,
        teaching_system_package_id: this.query.teaching_system_package_id,
        goods_id: this.query.goods_id,
        professional_id: this.query.professional_id
      }
      setAnswer({
        ...obj
      }).then(data => {
        console.log(data)
        console.log('提交成功')
      })
    },
    lookAnswer() {
      if (this.lists[this.current].stem_list[0].selected.length) {
        // 如果用户选择答案了 那么就是重做
        this.lists[this.current].lookAnswer = false
        this.lists[this.current].stem_list[0].selected = []
      } else {
        // 没选择答案就是查看解析
        this.lists[this.current].lookAnswer =
          !this.lists[this.current].lookAnswer
      }
      // if (this.lists[this.current]) {
      //   this.lists[this.current].lookAnswer =
      //     !this.lists[this.current].lookAnswer
      //   this.lists[this.current].selected = []
      // }
    },
    getList() {
      getQuestionsList({
        knowledge_id: this.knowledge_id,
        type: this.type,
        chapter_id: this.chapter_id,
        teaching_system_package_id: this.teaching_system_package_id,
        professional_id: this.query.professional_id,
        is_look_back: '2'
      }).then(data => {
        if (this.lists.length) {
          return
        }
        // if (this.query.disabled) {
        //   data.data.section_info = data.data.section_info.slice(0, 3)
        // }
        let list = setQuestionLists(data.data.section_info)
        this.product_id = data.data.product_id
        list.forEach((item, key) => {
          try {
            item.user_option = JSON.parse(item.user_option)
          } catch (error) {
            console.log(error)
            // this.$xh.Toast('数据返回错误！')
            item.user_option = []
          }
          if (isSelectedType(item.type)) {
            // 选择题处理
            let map = {}
            item.user_option.forEach(u_p => {
              map[u_p.sub_question_id] = u_p.answer.map(an => an * 1)
            })
            item.stem_list = item.stem_list.map(stem => {
              stem.selected = map[stem.id] ? map[stem.id] : []
              return stem
            })
          } else {
            // 填空题处理
            let map = {}
            item.user_option.forEach(u_p => {
              map[u_p.sub_question_id] = u_p.answer.map(an => an)
            })
            item.stem_list = item.stem_list.map(stem => {
              stem.selected = map[stem.id] ? map[stem.id] : []
              return stem
            })
          }
          item.questionNumber = key + 1
        })
        questionListData = list
        this.total = list.length
        this.getQuestionListData()
      })
    },
    getNextPage() {
      if (this.query.isfree == '1') {
        this.show = true
        // this.$xh.Toast('分享好友后可继续刷题')
      } else {
        this.getQuestionListData()
      }
    },
    getQuestionListData() {
      if (!this.errorStatic) {
        let length = this.lists.length
        let list = this.lists
        for (let i = length; i < length + 20; i++) {
          if (questionListData[i]) {
            list.push(questionListData[i])
          }
        }
      }
    },
    getNextChapter() {
      getNextChapter(this.knowledge_id)
    }
  },
  onUnload() {
    this.submit()
  },
  onLoad(e) {
    let that = this
    uni.enableAlertBeforeUnload({
      message: '学贵有恒，不要轻言放弃，确认退出吗？',
      success: function (res) {
        console.log('点击确认按钮了：', res)
        // 发送请求
      },
      fail: function (errMsg) {
        console.log('点击取消按钮了：', errMsg)
      }
    })
    this.query = e
    this.knowledge_id = e.knowledge_id
    this.type = e.type
    this.chapter_id = e.chapter_id
    this.isnextChapter = e.isnextChapter || 1
    //type==10
    if (this.type == 10) {
      this.teaching_system_package_id = e.teaching_system_package_id
    }
  },
  onShow() {
    this.getList()
  },
  onShareAppMessage() {
    return shareAppMessage2(() => {
      this.getQuestionListData()
      this.$refs.exercise.upSwiper(this.current)
      this.show = false
    })
  }
}
</script>
<style scoped lang="scss">
.share {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background-color: rgba($color: #000000, $alpha: 0.7);
  .share-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    // justify-content: center;
    margin-top: 158px;
    .logo {
      border-radius: 26px;
      width: 145px;
      height: 145px;
    }
    .text {
      padding-top: 73px;
      padding-bottom: 53px;
      font-size: 22px;
      color: #ffffff;
    }
    .share-but {
      width: 178px;
      text-align: center;
      height: 44px;
      line-height: 44px;
      background-color: #4d82fa;
      border-radius: 22px;
      font-size: 22px;
      color: #ffffff;
    }
  }
}
.page-baidu {
  .priview-time {
    position: fixed;
    left: 0;
    top: 140rpx;
    width: 100vw;
    height: 96rpx;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: center;
    padding: 0 40rpx;
    .nums {
      text {
        font-size: 32rpx;
        color: #000000;
      }
      text:last-child {
        color: #949494;
      }
    }
    .time {
      text {
        font-size: 32rpx;
        color: #000000;
      }
    }
    .back {
      position: absolute;
      // width: 19rpx;
      // height: 32rpx;
      left: 0rpx;
      padding: 0 30rpx;
      top: 0;
      height: 100%;
      display: flex;
      align-items: center;
      // bottom: 0;
      margin: auto 0;
      image {
        width: 19rpx;
        height: 32rpx;
      }
    }
    .error {
      position: absolute;
      left: 80rpx;
      top: 0;
      bottom: 0;
      margin: auto 0;
      width: 120rpx;
      height: 44rpx;
      text-align: center;
      line-height: 44rpx;
      background: #f5f5f5;
      border-radius: 8rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #999999;
    }
  }
  .uni-margin-wrap {
    width: calc(100vw);
    height: calc(100vh - 100rpx);
    overflow-y: scroll;
    padding-bottom: calc(80rpx + 42rpx + 36rpx);
    padding-top: 96rpx;
    box-sizing: border-box;
  }
}
</style>
