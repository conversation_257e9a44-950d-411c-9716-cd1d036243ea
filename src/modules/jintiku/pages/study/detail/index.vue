<template>
  <view class="detail">
    <ModuleStudyDetail :pageData="pageData"/>
  </view>
</template>

<script>
import ModuleStudyDetail from '../../../components/study/detail/index.vue'
export default {
  name: "StudyDetail",
  components: {
    ModuleStudyDetail
  },
  data(){
	return {
		pageData:{}
	}  
  },
  onLoad(e) {
	this.pageData = e;
	console.log(this.pageData,'00000000000000000')
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: '课程详情'
    });
  }
}
</script>
<style lang="less" scoped>

</style>
