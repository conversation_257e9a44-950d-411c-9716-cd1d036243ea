<template>
  <view class="wrap">
    <!-- uni-app中使用web-view组件来显示网页 -->
    <web-view :src="url" v-if="url" @error="onWebViewError"></web-view>
    <view v-else class="loading">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
export default {
  name: "StudyPdf",
  data() {
    return {
      url: '',
    };
  },
  onLoad(options) {
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: options.title || '文件预览'
    });

    // 构建PDF预览URL
    if (options.pdfUrl) {
      // 对URL进行解码，防止特殊字符问题
      const decodedUrl = decodeURIComponent(options.pdfUrl);
      this.url = '/pdf/web/viewer.html?file=' + encodeURIComponent(decodedUrl);
    } else {
      uni.showToast({
        title: '缺少文件地址',
        icon: 'none'
      });
    }
  },
  methods: {
    onWebViewError(e) {
      console.error('WebView加载错误:', e);
      uni.showToast({
        title: '文件加载失败',
        icon: 'none'
      });
    }
  }
};
</script>

<style lang="less" scoped>
.wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  bottom: 0;
  background-color: #f5f5f5;

  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    text {
      font-size: 16px;
      color: #666;
    }
  }
}
</style>

