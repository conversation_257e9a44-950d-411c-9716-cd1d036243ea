<template>
  <view class="detail">
    <!--  百家云  -->
    <ModuleStudyLiveBaijiayunLive :pageData="pageData"/>
  </view>
</template>
<script>

import { study } from '../../../api/index'
import ModuleStudyLiveBaijiayunLive from '../../../components/study/live/baijiayunLive.vue'
export default {
  name: "StudyLive",
  components:{
	  ModuleStudyLiveBaijiayunLive
  },
  data() {
    return {
      terminal: '2',
      lesson_id: '',
      data_id: '',
      time: null,
	  pageData:{}
    }
  },
  onLoad(options) {
	  this.pageData = options;
	  console.log(options,'33=options');
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: '直播详情'
    });

    // 获取终端类型
    const from = this.$store.state.jintiku.from || '';
    if (from == 'm') {
      this.terminal = '2'
    } else if (from == 'and') {
      this.terminal = '3'
    } else if (from == 'ios') {
      this.terminal = '4'
    }

    // 获取lesson_id
    if (options.lesson_id) {
      this.lesson_id = options.lesson_id
    }

    this.addData()
    this.time = setInterval(() => {
      this.addData()
    }, 1000 * 60 * 5)
  },
  onUnload() {
    // 页面卸载时清除定时器
    if (this.time) {
      clearInterval(this.time)
    }
  },
  methods: {
    addData() {
      study
        .addData({
          data_id: this.data_id,
          lesson_id: this.lesson_id,
          play_position: '',
          terminal: this.terminal - 0,
          type: 1,
          user_type: 1
        })
        .then((res) => {
          if (res.data.data_id) {
            this.data_id = res.data.data_id
          } else {
            this.data_id = ''
          }
        })
        .catch((err) => {
          console.log(err)
          console.log('记录学习失败')
        })
    }
  }
}
</script>
<style lang="less" scoped>

</style>
